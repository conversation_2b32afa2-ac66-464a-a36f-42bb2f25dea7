"use client"

import React, { useState, useRef, useCallback } from 'react';
import { useMediaStore, MediaFile, CropConfig } from '@/stores/media-store';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Upload, Trash2, Crop, Search, X, AlertTriangle } from 'lucide-react';
import { DeleteConfirmationDialog } from './DeleteConfirmationDialog';
import { AdvancedCropper } from './AdvancedCropper';

interface RobustMediaSelectorProps {
  open: boolean;
  onClose: () => void;
  onSelect: (media: MediaFile | undefined) => void;
  selectedMedia?: MediaFile | null;
  title?: string;
  description?: string;
  folder?: string;
  acceptedTypes?: string[];
  maxSizeMB?: number;
  cropConfig?: CropConfig;
}

export const RobustMediaSelector: React.FC<RobustMediaSelectorProps> = ({
  open,
  onClose,
  onSelect,
  selectedMedia,
  title = 'Select Media',
  description = '',
  folder = 'media',
  acceptedTypes = ['image/*'],
  maxSizeMB = 10,
  cropConfig
}) => {
  // Zustand store
  const {
    items,
    loading,
    error,
    selectedItem,
    uploadProgress,
    loadMedia,
    addItem,
    removeItem,
    selectItem,
    clearError,
    uploadFile,
    cropAndUpload
  } = useMediaStore();

  // Local state
  const [activeTab, setActiveTab] = useState<'gallery' | 'upload'>('gallery');
  const [searchQuery, setSearchQuery] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<MediaFile | null>(null);
  const [cropperOpen, setCropperOpen] = useState(false);
  const [imageToCrop, setImageToCrop] = useState<string | null>(null);
  const [dragActive, setDragActive] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load media when modal opens and gallery tab is active
  React.useEffect(() => {
    if (open && activeTab === 'gallery' && items.length === 0) {
      console.log('🔄 COMPONENT: Loading media on modal open');
      loadMedia(folder);
    }
  }, [open, activeTab, folder, loadMedia, items.length]);

  // Filter items based on search
  const filteredItems = items.filter(item =>
    item.originalName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Handle file selection
  const handleFileSelect = useCallback(async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const file = files[0];
    console.log(`📤 COMPONENT: Selected file ${file.name}`);

    // Validate file type
    if (!acceptedTypes.some(type => {
      if (type === 'image/*') return file.type.startsWith('image/');
      return file.type === type;
    })) {
      alert(`Invalid file type. Accepted types: ${acceptedTypes.join(', ')}`);
      return;
    }

    // Validate file size
    if (file.size > maxSizeMB * 1024 * 1024) {
      alert(`File too large. Maximum size: ${maxSizeMB}MB`);
      return;
    }

    // Upload file
    const uploadedFile = await uploadFile(file, folder, cropConfig);
    if (uploadedFile) {
      console.log(`✅ COMPONENT: File uploaded successfully`);
      // Switch to gallery tab to show uploaded file
      setActiveTab('gallery');
    }
  }, [acceptedTypes, maxSizeMB, uploadFile, folder, cropConfig]);

  // Handle drag and drop
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files);
    }
  }, [handleFileSelect]);

  // Handle item selection
  const handleItemSelect = (item: MediaFile) => {
    console.log(`🎯 COMPONENT: Selected item ${item.originalName}`);
    selectItem(item);
    onSelect(item);
  };

  // Handle delete request
  const handleDeleteRequest = (item: MediaFile) => {
    setItemToDelete(item);
    setDeleteDialogOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!itemToDelete) return;
    
    console.log(`🗑️ COMPONENT: Deleting ${itemToDelete.originalName}`);
    const success = await removeItem(itemToDelete.url);
    
    if (success) {
      // If deleted item was selected, clear selection
      if (selectedItem?.url === itemToDelete.url) {
        selectItem(null);
        onSelect(undefined);
      }
    }
    
    setDeleteDialogOpen(false);
    setItemToDelete(null);
  };

  // Handle crop request
  const handleCropRequest = (item: MediaFile) => {
    setImageToCrop(item.url);
    setCropperOpen(true);
  };

  // Handle crop complete
  const handleCropComplete = async (cropArea: any, config: CropConfig) => {
    if (!imageToCrop) return;
    
    console.log(`✂️ COMPONENT: Cropping ${imageToCrop}`);
    const croppedFile = await cropAndUpload(imageToCrop, cropArea, folder, config);
    
    if (croppedFile) {
      console.log(`✅ COMPONENT: Crop completed successfully`);
      setCropperOpen(false);
      setImageToCrop(null);
      // Auto-select the cropped image
      handleItemSelect(croppedFile);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <div>
                <div className="text-lg font-semibold">{title}</div>
                {description && <div className="text-sm text-gray-500 mt-1">{description}</div>}
              </div>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 overflow-hidden">
            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'gallery' | 'upload')}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="gallery">Gallery</TabsTrigger>
                <TabsTrigger value="upload">Upload</TabsTrigger>
              </TabsList>

              <TabsContent value="gallery" className="mt-4 space-y-4">
                {/* Search */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search files..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {/* Folder indicator */}
                <div className="text-xs text-gray-500 bg-purple-50 px-2 py-1 rounded inline-block">
                  📁 Folder: /{folder}/
                </div>

                {/* Error display */}
                {error && (
                  <div className="bg-red-50 border border-red-200 rounded p-3 flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-red-500" />
                    <span className="text-red-700 text-sm">{error}</span>
                    <Button variant="ghost" size="sm" onClick={clearError}>
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                )}

                {/* Loading state */}
                {loading && (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="text-sm text-gray-500 mt-2">Loading media...</p>
                  </div>
                )}

                {/* Gallery grid */}
                {!loading && (
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 max-h-96 overflow-y-auto">
                    {filteredItems.length === 0 ? (
                      <div className="col-span-full text-center py-8 text-gray-500">
                        {searchQuery ? 'No files match your search' : 'No files found'}
                      </div>
                    ) : (
                      filteredItems.map((item) => (
                        <div
                          key={item.url}
                          className={`relative group border-2 rounded-lg overflow-hidden cursor-pointer transition-all ${
                            selectedItem?.url === item.url
                              ? 'border-blue-500 ring-2 ring-blue-200'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => handleItemSelect(item)}
                        >
                          <img
                            src={item.url}
                            alt={item.originalName}
                            className="w-full h-24 object-cover"
                          />
                          
                          {/* Overlay with actions */}
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all flex items-center justify-center gap-2">
                            <Button
                              variant="secondary"
                              size="sm"
                              className="opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleCropRequest(item);
                              }}
                            >
                              <Crop className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="destructive"
                              size="sm"
                              className="opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteRequest(item);
                              }}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                          
                          {/* File name */}
                          <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white text-xs p-1 truncate">
                            {item.originalName}
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="upload" className="mt-4">
                {/* Upload area */}
                <div
                  className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                    dragActive
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                >
                  <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-lg font-medium text-gray-900 mb-2">
                    Drop files here or click to upload
                  </p>
                  <p className="text-sm text-gray-500 mb-4">
                    Supported formats: {acceptedTypes.join(', ')} • Max size: {maxSizeMB}MB
                  </p>
                  
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    disabled={uploadProgress > 0 && uploadProgress < 100}
                  >
                    Choose Files
                  </Button>
                  
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept={acceptedTypes.join(',')}
                    onChange={(e) => handleFileSelect(e.target.files)}
                    className="hidden"
                  />
                </div>

                {/* Upload progress */}
                {uploadProgress > 0 && uploadProgress < 100 && (
                  <div className="mt-4">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>Uploading...</span>
                      <span>{uploadProgress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all"
                        style={{ width: `${uploadProgress}%` }}
                      ></div>
                    </div>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </div>

          {/* Footer */}
          <div className="flex justify-between items-center pt-4 border-t">
            <div className="text-sm text-gray-500">
              {selectedItem ? `Selected: ${selectedItem.originalName}` : 'No file selected'}
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button
                onClick={() => {
                  onSelect(selectedItem || undefined);
                  onClose();
                }}
                disabled={!selectedItem}
              >
                Select Media
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete confirmation dialog */}
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleDeleteConfirm}
        mediaItem={itemToDelete}
      />

      {/* Cropper dialog */}
      <AdvancedCropper
        open={cropperOpen}
        onClose={() => setCropperOpen(false)}
        imageUrl={imageToCrop}
        onCropComplete={handleCropComplete}
        config={cropConfig}
      />
    </>
  );
};
