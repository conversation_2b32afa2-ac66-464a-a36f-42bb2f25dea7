"use client";

import React, { useCallback, useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Upload,
  FileImage,
  AlertCircle,
  CheckCircle,
  X,
  Plus,
} from 'lucide-react';

import { useMediaSelector } from '../../MediaSelectorProvider';
import { validateFiles, formatFileSize } from '../../utils/fileValidation';
import { dropZoneVariants, gridItemVariants } from '../../utils/animations';
import { cn } from '@/lib/utils';

export function DropZone() {
  const { state, actions } = useMediaSelector();
  const { config, uploading, uploadProgress } = state;
  const [dragActive, setDragActive] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);

  const labels = config.labels!;

  // Handle file drop
  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    console.log('🔥 DropZone onDrop called:', { acceptedFiles, rejectedFiles });
    setValidationErrors([]);

    // Validate files
    const validation = validateFiles(
      acceptedFiles,
      config.acceptedTypes,
      config.maxFileSize,
      config.maxFiles
    );

    console.log('🔍 File validation result:', validation);

    if (!validation.valid) {
      console.log('❌ Validation failed:', validation.errors);
      setValidationErrors(validation.errors);
      return;
    }

    // Handle rejected files
    if (rejectedFiles.length > 0) {
      const errors = rejectedFiles.map(({ file, errors }) =>
        `${file.name}: ${errors.map((e: any) => e.message).join(', ')}`
      );
      console.log('❌ Rejected files:', errors);
      setValidationErrors(errors);
      return;
    }

    // Upload files
    console.log('✅ Starting upload for files:', acceptedFiles.map(f => f.name));
    actions.uploadFiles(acceptedFiles);
  }, [config, actions]);

  const acceptTypes = config.acceptedTypes.reduce((acc, type) => {
    acc[type] = [];
    return acc;
  }, {} as Record<string, string[]>);

  console.log('🎯 DropZone config:', {
    acceptedTypes: config.acceptedTypes,
    acceptTypes,
    maxFileSize: config.maxFileSize,
    maxFiles: config.maxFiles,
    noClick: false
  });

  const { getRootProps, getInputProps, isDragActive, open } = useDropzone({
    onDrop,
    accept: acceptTypes,
    maxSize: config.maxFileSize,
    maxFiles: config.maxFiles,
    onDragEnter: () => {
      console.log('🔵 Drag enter');
      setDragActive(true);
    },
    onDragLeave: () => {
      console.log('🔴 Drag leave');
      setDragActive(false);
    },
    noClick: false, // Allow clicking on the entire drop zone
    onClick: () => {
      console.log('🖱️ DropZone clicked - opening file dialog');
    },
    onDropAccepted: (files) => {
      console.log('✅ Files accepted:', files.map(f => f.name));
    },
    onDropRejected: (rejectedFiles) => {
      console.log('❌ Files rejected:', rejectedFiles);
    }
  });

  const rootProps = getRootProps();
  const inputProps = getInputProps();
  console.log('🎯 getRootProps result:', rootProps);
  console.log('🎯 getInputProps result:', inputProps);

  const uploadProgressValues = Object.values(uploadProgress);
  const averageProgress = uploadProgressValues.length > 0
    ? uploadProgressValues.reduce((sum, progress) => sum + progress, 0) / uploadProgressValues.length
    : 0;

  console.log('🎨 DropZone render:', {
    isDragActive,
    uploading,
    validationErrors: validationErrors.length,
    dragActive,
    averageProgress
  });

  return (
    <div className="space-y-4">
      {/* Drop zone */}
      <motion.div
        {...rootProps}
        variants={dropZoneVariants}
        animate={isDragActive ? 'dragOver' : 'idle'}
        className={cn(
          "relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 cursor-pointer",
          "bg-gradient-to-br from-blue-50/50 to-purple-50/50 dark:from-blue-950/20 dark:to-purple-950/20",
          "hover:from-blue-100/50 hover:to-purple-100/50 dark:hover:from-blue-900/30 dark:hover:to-purple-900/30",
          isDragActive
            ? "border-blue-500 bg-blue-50 dark:bg-blue-950/30"
            : "border-gray-300 dark:border-gray-600",
          uploading && "pointer-events-none opacity-50"
        )}
        onClick={(e) => {
          console.log('🖱️ Direct onClick triggered on DropZone div', e);
          console.log('🖱️ Calling open() function');
          console.log('🖱️ open function type:', typeof open);

          // Try react-dropzone's open function first
          try {
            const result = open();
            console.log('🖱️ open() result:', result);
          } catch (error) {
            console.error('❌ Error calling open():', error);
          }

          // Also try manual input click as fallback
          console.log('🖱️ Trying manual input click');
          console.log('🖱️ inputRef.current:', inputRef.current);
          if (inputRef.current) {
            try {
              inputRef.current.click();
              console.log('✅ Manual input click successful');
            } catch (error) {
              console.error('❌ Error with manual click:', error);
            }
          }
        }}
        onMouseDown={(e) => {
          console.log('🖱️ MouseDown on DropZone', e);
        }}
        onMouseUp={(e) => {
          console.log('🖱️ MouseUp on DropZone', e);
        }}
      >
        <input
          {...inputProps}
          ref={(el) => {
            console.log('🎯 Input element ref:', el);
            inputRef.current = el;
            if (el) {
              console.log('🎯 Input element properties:', {
                type: el.type,
                accept: el.accept,
                multiple: el.multiple,
                style: el.style.cssText
              });
            }
          }}
        />
        
        <motion.div
          variants={gridItemVariants}
          className="space-y-4"
        >
          {/* Upload icon */}
          <motion.div
            animate={isDragActive ? { scale: 1.1 } : { scale: 1 }}
            className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center"
          >
            <Upload className="h-8 w-8 text-white" />
          </motion.div>

          {/* Upload text */}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              {isDragActive ? labels.dragDropText : labels.uploadDescription}
            </h3>
            <p className="text-sm text-muted-foreground">
              {config.acceptedTypes.join(', ')} • Max {formatFileSize(config.maxFileSize)}
              {config.maxFiles && ` • Up to ${config.maxFiles} files`}
            </p>
          </div>

          {/* Browse button */}
          <Button
            variant="outline"
            className="bg-white/80 backdrop-blur-sm hover:bg-white"
          >
            <Plus className="h-4 w-4 mr-2" />
            {labels.browseFiles}
          </Button>
        </motion.div>

        {/* Upload overlay */}
        {uploading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="absolute inset-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-xl flex items-center justify-center"
          >
            <div className="text-center space-y-4">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="mx-auto w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full"
              />
              <div>
                <p className="font-medium">{labels.uploadProgress}</p>
                <Progress value={averageProgress} className="w-48 mt-2" />
                <p className="text-sm text-muted-foreground mt-1">
                  {Math.round(averageProgress)}% complete
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </motion.div>

      {/* Validation errors */}
      {validationErrors.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg p-4"
        >
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-red-600 flex-shrink-0 mt-0.5" />
            <div className="flex-1">
              <h4 className="font-medium text-red-800 dark:text-red-200">
                Upload Error
              </h4>
              <ul className="mt-2 space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index} className="text-sm text-red-700 dark:text-red-300">
                    {error}
                  </li>
                ))}
              </ul>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setValidationErrors([])}
              className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </motion.div>
      )}

      {/* File type examples */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {[
          { type: 'Images', icon: '🖼️', formats: 'JPG, PNG, WebP' },
          { type: 'Documents', icon: '📄', formats: 'PDF, DOC, TXT' },
          { type: 'Videos', icon: '🎥', formats: 'MP4, WebM, MOV' },
          { type: 'Archives', icon: '📦', formats: 'ZIP, RAR, 7Z' },
        ].map((category) => (
          <motion.div
            key={category.type}
            variants={gridItemVariants}
            className="text-center p-4 bg-white/50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700"
          >
            <div className="text-2xl mb-2">{category.icon}</div>
            <div className="font-medium text-sm">{category.type}</div>
            <div className="text-xs text-muted-foreground">{category.formats}</div>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
