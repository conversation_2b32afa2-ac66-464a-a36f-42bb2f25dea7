import { NextRequest, NextResponse } from "next/server"
import { writeFile, mkdir } from "fs/promises"
import { join } from "path"
import { existsSync } from "fs"
import sharp from "sharp"

export async function POST(request: NextRequest) {
  try {
    const data = await request.formData()
    const file: File | null = data.get("file") as unknown as File
    const folder: string = data.get("folder") as string || "media"
    const targetWidth: string = data.get("targetWidth") as string
    const targetHeight: string = data.get("targetHeight") as string

    if (!file) {
      return NextResponse.json({ error: "No file received" }, { status: 400 })
    }

    // Validate file extension
    const allowedExtensions = ["jpg", "jpeg", "png", "webp"];
    const fileExtension = file.name.split(".").pop()?.toLowerCase() || "";
    if (!allowedExtensions.includes(fileExtension)) {
      return NextResponse.json({ 
        error: "Invalid file extension. Only JPEG, PNG, and WebP are allowed." 
      }, { status: 400 })
    }

    // Validate file size (2MB max)
    const maxSize = 2 * 1024 * 1024 // 2MB
    if (file.size > maxSize) {
      return NextResponse.json({ 
        error: "File too large. Maximum size is 2MB." 
      }, { status: 400 })
    }

    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Create unique filename
    const timestamp = Date.now()
    const safeName = file.name.replace(/[^a-zA-Z0-9.-]/g, "_")
    const filename = `${timestamp}-${safeName}`
    const baseFilename = filename.replace(/\.[^.]+$/, "")
    
    // Create upload directory
    const uploadDir = join(process.cwd(), "public", folder)
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true })
    }

    // Write original file
    const filePath = join(uploadDir, filename)
    await writeFile(filePath, buffer)

    // If JPEG or PNG, convert to WebP
    let webpUrl = null
    if (["jpg", "jpeg", "png"].includes(fileExtension)) {
      const webpFilename = `${baseFilename}.webp`
      const webpPath = join(uploadDir, webpFilename)
      await sharp(buffer)
        .webp({ quality: 80 })
        .toFile(webpPath)
      webpUrl = `/${folder}/${webpFilename}`
    }

    // Generate URL
    const fileUrl = `/${folder}/${filename}`

    // Create media file object
    const mediaFile = {
      id: timestamp,
      filename: `${folder}/${filename}`,
      originalName: file.name,
      mimeType: file.type,
      size: file.size,
      url: fileUrl,
      webpUrl,
      thumbnailSmall: webpUrl || fileUrl,
      thumbnailMedium: webpUrl || fileUrl,
      thumbnailLarge: webpUrl || fileUrl,
      createdAt: new Date().toISOString()
    }

    return NextResponse.json({
      success: true,
      data: mediaFile,
      message: "File uploaded and optimized successfully"
    })
  } catch (error) {
    console.error("Upload error:", error)
    return NextResponse.json({ 
      error: "Failed to upload file" 
    }, { status: 500 })
  }
}
