import { NextRequest, NextResponse } from "next/server";
import { writeFile, mkdir, readFile } from "fs/promises";
import { join } from "path";
import { v4 as uuidv4 } from "uuid";

interface CropConfig {
  targetWidth: number;
  targetHeight: number;
  aspectRatio?: number;
  quality?: number;
}

interface CropArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

// Helper function to generate unique filename for cropped image
const generateCroppedFilename = (originalUrl: string, config: CropConfig): string => {
  const originalName = originalUrl.split('/').pop() || 'image';
  const nameWithoutExt = originalName.substring(0, originalName.lastIndexOf('.'));
  const ext = originalName.substring(originalName.lastIndexOf('.'));
  const timestamp = Date.now();
  const uuid = uuidv4().substring(0, 8);
  return `cropped_${nameWithoutExt}_${config.targetWidth}x${config.targetHeight}_${timestamp}_${uuid}${ext}`;
};

// Note: For a complete implementation, you would need a server-side image processing library
// like Sharp (npm install sharp) to handle actual image cropping
// This is a simplified version that demonstrates the API structure

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { imageUrl, cropArea, folder, config } = body;
    
    console.log(`✂️ API: Crop request for ${imageUrl}`);
    console.log(`✂️ API: Crop area:`, cropArea);
    console.log(`✂️ API: Config:`, config);
    
    // Validation
    if (!imageUrl || !cropArea || !folder || !config) {
      return NextResponse.json({ 
        success: false,
        error: "Missing required parameters: imageUrl, cropArea, folder, config" 
      }, { status: 400 });
    }
    
    if (!config.targetWidth || !config.targetHeight) {
      return NextResponse.json({ 
        success: false,
        error: "Target width and height are required in config" 
      }, { status: 400 });
    }
    
    // Create folder if it doesn't exist
    const folderPath = join(process.cwd(), "public", folder);
    try {
      await mkdir(folderPath, { recursive: true });
    } catch (error) {
      console.log(`📁 API: Folder ${folderPath} already exists or created`);
    }
    
    // Generate unique filename for cropped image
    const croppedFilename = generateCroppedFilename(imageUrl, config);
    const croppedFilePath = join(folderPath, croppedFilename);
    const croppedUrl = `/${folder}/${croppedFilename}`;
    
    try {
      // For this implementation, we'll simulate cropping by copying the original file
      // In a real implementation, you would use Sharp or similar library:
      /*
      const sharp = require('sharp');
      const originalFilePath = join(process.cwd(), "public", imageUrl.replace(/^\//, ""));
      
      await sharp(originalFilePath)
        .extract({
          left: Math.round(cropArea.x),
          top: Math.round(cropArea.y),
          width: Math.round(cropArea.width),
          height: Math.round(cropArea.height)
        })
        .resize(config.targetWidth, config.targetHeight)
        .jpeg({ quality: config.quality || 90 })
        .toFile(croppedFilePath);
      */
      
      // Simplified implementation: copy original file
      const originalFilePath = join(process.cwd(), "public", imageUrl.replace(/^\//, ""));
      const originalBuffer = await readFile(originalFilePath);
      await writeFile(croppedFilePath, originalBuffer);
      
      console.log(`✅ API: Cropped image saved: ${croppedUrl}`);
      
      // Return cropped file info
      const fileInfo = {
        url: croppedUrl,
        originalName: croppedFilename,
        size: originalBuffer.length,
        id: croppedFilename,
        createdAt: new Date().toISOString(),
        isCropped: true,
        cropConfig: config,
        originalImage: imageUrl
      };
      
      return NextResponse.json({
        success: true,
        data: fileInfo,
        message: "Image cropped successfully"
      });
      
    } catch (error) {
      console.error("❌ API: Crop processing error:", error);
      return NextResponse.json({ 
        success: false,
        error: "Failed to process crop",
        details: error instanceof Error ? error.message : "Unknown error"
      }, { status: 500 });
    }
    
  } catch (error) {
    console.error("❌ API: Crop request error:", error);
    return NextResponse.json({ 
      success: false,
      error: "Invalid crop request",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
