import { useState, useCallback } from 'react';
import type { MediaFile, MediaSelectorConfig } from '../types';
import { DEFAULT_CONFIG, mergeConfig } from '../types/config';

// Simple hook for basic media selector usage without provider
export function useSimpleMediaSelector(config: Partial<MediaSelectorConfig>) {
  const [open, setOpen] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<MediaFile[]>([]);
  
  const finalConfig = mergeConfig(DEFAULT_CONFIG, {
    ...config,
    onSelect: (files: MediaFile[]) => {
      setSelectedFiles(files);
      config.onSelect?.(files);
      setOpen(false);
    },
  });

  const openSelector = useCallback(() => {
    setOpen(true);
  }, []);

  const closeSelector = useCallback(() => {
    setOpen(false);
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedFiles([]);
  }, []);

  return {
    open,
    selectedFiles,
    config: finalConfig,
    openSelector,
    closeSelector,
    clearSelection,
  };
}

// Hook for project images (convenience)
export function useProjectImageSelector(onSelect?: (file: MediaFile | null) => void) {
  const [selectedFile, setSelectedFile] = useState<MediaFile | null>(null);
  
  return useSimpleMediaSelector({
    folder: 'projeler',
    acceptedTypes: ['image/jpeg', 'image/png', 'image/webp'],
    maxFileSize: 5 * 1024 * 1024, // 5MB
    maxFiles: 1,
    cropConfig: {
      targetWidth: 800,
      targetHeight: 600,
      quality: 90,
    },
    labels: {
      title: 'Proje Görseli Seç',
      description: 'Projeye ait görseli seçin veya yükleyin',
    },
    onSelect: (files) => {
      const file = files[0] || null;
      setSelectedFile(file);
      onSelect?.(file);
    },
  });
}

// Hook for blog images
export function useBlogImageSelector(onSelect?: (files: MediaFile[]) => void) {
  return useSimpleMediaSelector({
    folder: 'blog',
    acceptedTypes: ['image/*'],
    maxFileSize: 2 * 1024 * 1024, // 2MB
    cropConfig: {
      targetWidth: 1200,
      targetHeight: 630,
      aspectRatio: 1200 / 630,
      quality: 85,
    },
    labels: {
      title: 'Blog Görseli Seç',
      description: 'Blog yazısı için görsel seçin',
    },
    onSelect,
  });
}

// Hook for product images
export function useProductImageSelector(onSelect?: (files: MediaFile[]) => void) {
  return useSimpleMediaSelector({
    folder: 'products',
    acceptedTypes: ['image/jpeg', 'image/png', 'image/webp'],
    maxFileSize: 3 * 1024 * 1024, // 3MB
    maxFiles: 5,
    cropConfig: {
      aspectRatio: 1, // Square
      targetWidth: 800,
      targetHeight: 800,
      quality: 90,
    },
    labels: {
      title: 'Ürün Görseli Seç',
      description: 'Ürün için görseller seçin',
    },
    onSelect,
  });
}

// Hook for document uploads
export function useDocumentSelector(onSelect?: (files: MediaFile[]) => void) {
  return useSimpleMediaSelector({
    folder: 'documents',
    acceptedTypes: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
    ],
    maxFileSize: 10 * 1024 * 1024, // 10MB
    showMetadata: false,
    enableBulkActions: true,
    labels: {
      title: 'Belge Seç',
      description: 'Belge dosyalarını seçin veya yükleyin',
    },
    onSelect,
  });
}
