"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { MediaSelector, useProjectImageSelector, type MediaFile } from '@/components/media-selector';

export default function TestMediaSelectorPage() {
  const [open, setOpen] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<MediaFile[]>([]);

  // Test basic MediaSelector
  const handleSelect = (files: MediaFile[]) => {
    setSelectedFiles(files);
    setOpen(false);
  };

  // Test convenience hook
  const projectSelector = useProjectImageSelector((file) => {
    console.log('Project image selected:', file);
    if (file) setSelectedFiles([file]);
  });

  return (
    <div className="p-8 space-y-6">
      <h1 className="text-2xl font-bold">Media Selector Test Page</h1>
      
      <div className="space-y-4">
        <div>
          <h2 className="text-lg font-semibold mb-2">Basic MediaSelector Test</h2>
          <Button onClick={() => setOpen(true)}>
            Open Media Selector
          </Button>
        </div>

        <div>
          <h2 className="text-lg font-semibold mb-2">Project Image Selector Test</h2>
          <Button onClick={projectSelector.openSelector} variant="outline">
            Open Project Image Selector
          </Button>
        </div>

        {selectedFiles.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold mb-2">Selected Files:</h2>
            <div className="space-y-2">
              {selectedFiles.map((file) => (
                <div key={file.id} className="p-2 border rounded">
                  <p><strong>Name:</strong> {file.originalName}</p>
                  <p><strong>Size:</strong> {(file.size / 1024 / 1024).toFixed(2)} MB</p>
                  <p><strong>Type:</strong> {file.mimeType}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Basic MediaSelector */}
      <MediaSelector
        open={open}
        onClose={() => setOpen(false)}
        config={{
          folder: 'test',
          acceptedTypes: ['image/*'],
          maxFileSize: 5 * 1024 * 1024,
          maxFiles: 5,
          layout: 'dialog',
          theme: 'auto',
          showMetadata: true,
          enableBulkActions: true,
          enableSearch: true,
          enableUpload: true,
          labels: {
            title: 'Test Media Selector',
            description: 'Select test images',
          },
          onSelect: handleSelect,
        }}
      />

      {/* Project Image Selector */}
      <MediaSelector {...projectSelector} />
    </div>
  );
}
