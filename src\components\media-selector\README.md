# Enterprise Media Selector

A modern, highly configurable, and feature-rich media selector component built with React, TypeScript, and Framer Motion. Designed for enterprise applications with advanced features like bulk operations, real-time progress tracking, and metadata editing.

## ✨ Features

### 🎨 Modern Design
- **Glass-morphism effects** with backdrop blur and subtle borders
- **Gradient backgrounds** and modern CSS styling
- **Framer Motion animations** with smooth transitions and micro-interactions
- **Responsive masonry grid** layout for optimal space usage
- **Dark/light theme support** with automatic detection

### 🚀 Advanced Functionality
- **Real-time upload progress** with KPI cards and detailed tracking
- **Bulk operations** for multi-select, batch delete, and mass actions
- **Advanced search and filtering** by name, type, size, date, and tags
- **Metadata editing** with alt text, captions, and tag management
- **Image cropping and editing** with configurable aspect ratios
- **Drag & drop support** with visual feedback

### ⚡ Performance & Architecture
- **Context-based state management** with reducer pattern
- **TypeScript-first** with comprehensive type definitions
- **Optimistic updates** for better user experience
- **Error handling** with retry mechanisms
- **Accessibility support** with keyboard navigation and screen readers

### 🔧 Developer Experience
- **Highly configurable** with preset configurations
- **Multiple layout modes** (dialog, inline, compact)
- **Extensible API** with hooks and utilities
- **Enterprise-ready** with production-grade features

## 🚀 Quick Start

### Basic Usage

```tsx
import { MediaSelector } from '@/components/media-selector';

function MyComponent() {
  const [open, setOpen] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);

  return (
    <>
      <button onClick={() => setOpen(true)}>
        Select Media
      </button>
      
      <MediaSelector
        open={open}
        onClose={() => setOpen(false)}
        config={{
          folder: 'my-folder',
          acceptedTypes: ['image/*'],
          maxFileSize: 5 * 1024 * 1024, // 5MB
          onSelect: (files) => {
            setSelectedFiles(files);
            setOpen(false);
          },
        }}
      />
    </>
  );
}
```

### Using Convenience Hooks

```tsx
import { useProjectImageSelector } from '@/components/media-selector';

function ProjectForm() {
  const mediaSelector = useProjectImageSelector((file) => {
    console.log('Selected:', file);
  });

  return (
    <>
      <button onClick={mediaSelector.openSelector}>
        Select Project Image
      </button>
      
      <MediaSelector {...mediaSelector} />
    </>
  );
}
```

## 📋 Configuration

### MediaSelectorConfig

```typescript
interface MediaSelectorConfig {
  // Storage settings
  folder: string;
  apiEndpoints: {
    upload: string;
    list: string;
    delete?: string;
  };
  
  // File restrictions
  acceptedTypes: string[];
  maxFileSize: number;
  maxFiles?: number;
  
  // UI customization
  layout: 'dialog' | 'inline' | 'compact';
  theme: 'light' | 'dark' | 'auto';
  showMetadata: boolean;
  enableBulkActions: boolean;
  enableSearch: boolean;
  enableUpload: boolean;
  
  // Image processing
  cropConfig?: {
    aspectRatio?: number;
    targetWidth?: number;
    targetHeight?: number;
    quality?: number;
  };
  
  // Callbacks
  onSelect?: (files: MediaFile[]) => void;
  onUpload?: (file: MediaFile) => void;
  onDelete?: (fileId: string) => void;
  
  // Localization
  labels?: Partial<MediaSelectorLabels>;
}
```

### Preset Configurations

```tsx
import { 
  PROJECT_IMAGES_CONFIG,
  BLOG_IMAGES_CONFIG,
  PRODUCT_IMAGES_CONFIG,
  DOCUMENT_CONFIG 
} from '@/components/media-selector';

// Use preset configurations
<MediaSelector
  config={{
    ...PROJECT_IMAGES_CONFIG,
    onSelect: handleSelect,
  }}
/>
```

## 🎯 Layout Modes

### Dialog Mode (Default)
Full-screen modal dialog with tabs and advanced features.

```tsx
<MediaSelector
  config={{
    layout: 'dialog',
    // ... other config
  }}
/>
```

### Inline Mode
Embedded component for integration into forms or pages.

```tsx
<MediaSelector
  config={{
    layout: 'inline',
    // ... other config
  }}
/>
```

### Compact Mode
Minimal interface for space-constrained areas.

```tsx
<MediaSelector
  config={{
    layout: 'compact',
    // ... other config
  }}
/>
```

## 🔌 API Integration

The component works with your existing API endpoints:

### Upload Endpoint (`/api/upload`)
```typescript
// Expected request format
FormData {
  file: File,
  folder: string,
  alt?: string,
  caption?: string,
  tags?: string[], // JSON stringified
  cropConfig?: object // JSON stringified
}

// Expected response format
{
  success: boolean,
  data?: MediaFile,
  error?: string
}
```

### List Endpoint (`/api/media`)
```typescript
// Query parameters
{
  folder?: string,
  search?: string,
  type?: string,
  page?: number,
  pageSize?: number
}

// Expected response format
{
  success: boolean,
  data?: MediaFile[],
  error?: string
}
```

## 🎨 Customization

### Custom Labels (Localization)

```tsx
<MediaSelector
  config={{
    labels: {
      title: 'Medya Seçici',
      description: 'Dosyalarınızı seçin',
      uploadTitle: 'Yükle',
      galleryTitle: 'Galeri',
      // ... other labels
    },
  }}
/>
```

### Custom Styling

The component uses Tailwind CSS classes and CSS variables for theming. You can customize the appearance by overriding CSS variables or using custom classes.

## 🔧 Advanced Usage

### With Provider for Complex State

```tsx
import { MediaSelectorProvider, useMediaSelector } from '@/components/media-selector';

function App() {
  return (
    <MediaSelectorProvider config={myConfig}>
      <MyComplexComponent />
    </MediaSelectorProvider>
  );
}

function MyComplexComponent() {
  const { state, actions } = useMediaSelector();
  
  // Access to full state and actions
  return (
    <div>
      <p>Selected: {state.selectedItems.size} files</p>
      <button onClick={actions.selectAll}>Select All</button>
      {/* Custom UI using the state */}
    </div>
  );
}
```

### Custom Upload Queue

```tsx
import { UploadQueue } from '@/components/media-selector';

const uploadQueue = new UploadQueue(5); // Max 5 concurrent uploads

// Add files to queue
await uploadQueue.add(file, (progress) => {
  console.log(`Upload progress: ${progress.percentage}%`);
});
```

## 📱 Responsive Design

The component is fully responsive and adapts to different screen sizes:

- **Mobile**: Single column grid, touch-friendly interactions
- **Tablet**: 2-3 column grid, optimized for touch
- **Desktop**: 4-6 column grid, hover effects, keyboard shortcuts

## ♿ Accessibility

- **Keyboard navigation** with arrow keys and shortcuts
- **Screen reader support** with ARIA labels and descriptions
- **Focus management** with proper tab order
- **High contrast support** for better visibility

## 🧪 Testing

```tsx
import { render, screen } from '@testing-library/react';
import { MediaSelector } from '@/components/media-selector';

test('renders media selector', () => {
  render(
    <MediaSelector
      open={true}
      config={{
        folder: 'test',
        acceptedTypes: ['image/*'],
        maxFileSize: 1024 * 1024,
      }}
    />
  );
  
  expect(screen.getByText('Media Selector')).toBeInTheDocument();
});
```

## 📦 Dependencies

- React 18+
- TypeScript 4.5+
- Framer Motion 10+
- react-dropzone 14+
- react-easy-crop 4+
- Tailwind CSS 3+

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.
