"use client";

import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import { useMediaSelector } from '../../MediaSelectorProvider';
import { MediaItem } from './MediaItem';
import { LoadingStates } from '../UI/LoadingStates';
import { EmptyStates } from '../UI/EmptyStates';
import { containerVariants, gridItemVariants } from '../../utils/animations';
import { cn } from '@/lib/utils';

export function MediaGrid() {
  const { state, actions } = useMediaSelector();
  const { items, loading, selectedItems, view, searchQuery, filters, config } = state;

  // Filter items based on search and filters
  const filteredItems = useMemo(() => {
    let filtered = [...items];

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item =>
        item.originalName.toLowerCase().includes(query) ||
        item.alt?.toLowerCase().includes(query) ||
        item.caption?.toLowerCase().includes(query) ||
        item.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Type filter
    if (filters.type && filters.type !== 'all') {
      filtered = filtered.filter(item => {
        const category = item.mimeType.split('/')[0];
        return category === filters.type;
      });
    }

    // Date range filter
    if (filters.dateRange) {
      filtered = filtered.filter(item => {
        const itemDate = new Date(item.createdAt);
        return itemDate >= filters.dateRange!.start && itemDate <= filters.dateRange!.end;
      });
    }

    // Size range filter
    if (filters.sizeRange) {
      filtered = filtered.filter(item =>
        item.size >= filters.sizeRange!.min && item.size <= filters.sizeRange!.max
      );
    }

    // Tags filter
    if (filters.tags && filters.tags.length > 0) {
      filtered = filtered.filter(item =>
        item.tags?.some(tag => filters.tags!.includes(tag))
      );
    }

    return filtered;
  }, [items, searchQuery, filters]);

  // Loading state
  if (loading) {
    return <LoadingStates view={view} />;
  }

  // Empty state
  if (filteredItems.length === 0) {
    if (items.length === 0) {
      return <EmptyStates type="empty" />;
    } else {
      return <EmptyStates type="no-results" query={searchQuery} />;
    }
  }

  // Grid view
  if (view === 'grid') {
    return (
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="p-6"
      >
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
          {filteredItems.map((item, index) => (
            <motion.div
              key={item.id}
              variants={gridItemVariants}
              custom={index}
              layout
              className="aspect-square"
            >
              <MediaItem
                item={item}
                selected={selectedItems.has(item.id)}
                onSelect={() => actions.selectItem(item.id)}
                onToggleSelection={() => actions.toggleSelection(item.id)}
                onEdit={() => actions.startEdit(item)}
                onCrop={() => actions.startCrop(item)}
                onDelete={() => actions.deleteItem(item.id)}
                view="grid"
              />
            </motion.div>
          ))}
        </div>
      </motion.div>
    );
  }

  // List view
  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="p-6"
    >
      <div className="space-y-2">
        {filteredItems.map((item, index) => (
          <motion.div
            key={item.id}
            variants={gridItemVariants}
            custom={index}
            layout
            className="w-full"
          >
            <MediaItem
              item={item}
              selected={selectedItems.has(item.id)}
              onSelect={() => actions.selectItem(item.id)}
              onToggleSelection={() => actions.toggleSelection(item.id)}
              onEdit={() => actions.startEdit(item)}
              onCrop={() => actions.startCrop(item)}
              onDelete={() => actions.deleteItem(item.id)}
              view="list"
            />
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
}
