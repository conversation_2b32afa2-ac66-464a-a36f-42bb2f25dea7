// Framer Motion animation variants and utilities

import { Variants } from 'framer-motion';

// Container animations
export const containerVariants: Variants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.3,
      staggerChildren: 0.1,
    },
  },
  exit: {
    opacity: 0,
    transition: {
      duration: 0.2,
    },
  },
};

// Grid item animations
export const gridItemVariants: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
    y: 20,
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 24,
    },
  },
  hover: {
    scale: 1.05,
    transition: {
      type: 'spring',
      stiffness: 400,
      damping: 25,
    },
  },
  tap: {
    scale: 0.95,
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    transition: {
      duration: 0.2,
    },
  },
};

// Modal/Dialog animations
export const modalVariants: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.9,
    y: 20,
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 30,
    },
  },
  exit: {
    opacity: 0,
    scale: 0.9,
    y: 20,
    transition: {
      duration: 0.2,
    },
  },
};

// Backdrop animations
export const backdropVariants: Variants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.3,
    },
  },
  exit: {
    opacity: 0,
    transition: {
      duration: 0.2,
    },
  },
};

// Tab animations
export const tabVariants: Variants = {
  hidden: {
    opacity: 0,
    x: -20,
  },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.3,
    },
  },
  exit: {
    opacity: 0,
    x: 20,
    transition: {
      duration: 0.2,
    },
  },
};

// Upload progress animations
export const progressVariants: Variants = {
  hidden: {
    scaleX: 0,
  },
  visible: {
    scaleX: 1,
    transition: {
      duration: 0.3,
      ease: 'easeOut',
    },
  },
};

// Button animations
export const buttonVariants: Variants = {
  idle: {
    scale: 1,
  },
  hover: {
    scale: 1.05,
    transition: {
      type: 'spring',
      stiffness: 400,
      damping: 25,
    },
  },
  tap: {
    scale: 0.95,
  },
  loading: {
    scale: 1,
    opacity: 0.7,
  },
};

// Drag and drop animations
export const dropZoneVariants: Variants = {
  idle: {
    borderColor: 'rgba(156, 163, 175, 0.3)',
    backgroundColor: 'rgba(249, 250, 251, 0.5)',
  },
  dragOver: {
    borderColor: 'rgba(59, 130, 246, 0.8)',
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    scale: 1.02,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 25,
    },
  },
  dragLeave: {
    borderColor: 'rgba(156, 163, 175, 0.3)',
    backgroundColor: 'rgba(249, 250, 251, 0.5)',
    scale: 1,
  },
};

// Search input animations
export const searchVariants: Variants = {
  focused: {
    scale: 1.02,
    boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.1)',
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 25,
    },
  },
  blur: {
    scale: 1,
    boxShadow: '0 0 0 0px rgba(59, 130, 246, 0)',
  },
};

// Loading spinner animations
export const spinnerVariants: Variants = {
  animate: {
    rotate: 360,
    transition: {
      duration: 1,
      repeat: Infinity,
      ease: 'linear',
    },
  },
};

// Notification/Toast animations
export const toastVariants: Variants = {
  hidden: {
    opacity: 0,
    y: -50,
    scale: 0.9,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 25,
    },
  },
  exit: {
    opacity: 0,
    y: -50,
    scale: 0.9,
    transition: {
      duration: 0.2,
    },
  },
};

// Slide animations for panels
export const slideVariants: Variants = {
  hidden: {
    x: '100%',
  },
  visible: {
    x: 0,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 30,
    },
  },
  exit: {
    x: '100%',
    transition: {
      duration: 0.3,
    },
  },
};

// Fade animations
export const fadeVariants: Variants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.3,
    },
  },
  exit: {
    opacity: 0,
    transition: {
      duration: 0.2,
    },
  },
};

// Scale animations
export const scaleVariants: Variants = {
  hidden: {
    scale: 0,
    opacity: 0,
  },
  visible: {
    scale: 1,
    opacity: 1,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 25,
    },
  },
  exit: {
    scale: 0,
    opacity: 0,
    transition: {
      duration: 0.2,
    },
  },
};

// Stagger animation utility
export const staggerContainer = (staggerChildren: number = 0.1): Variants => ({
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren,
      delayChildren: 0.1,
    },
  },
});

// Custom spring configuration
export const springConfig = {
  type: 'spring' as const,
  stiffness: 300,
  damping: 25,
};

// Custom easing functions
export const customEasing = {
  easeOutCubic: [0.33, 1, 0.68, 1],
  easeInOutCubic: [0.65, 0, 0.35, 1],
  easeOutQuart: [0.25, 1, 0.5, 1],
  easeInOutQuart: [0.76, 0, 0.24, 1],
};

// Animation duration constants
export const ANIMATION_DURATION = {
  fast: 0.15,
  normal: 0.3,
  slow: 0.5,
} as const;

// Utility function to create custom variants
export function createCustomVariants(
  hidden: Record<string, any>,
  visible: Record<string, any>,
  exit?: Record<string, any>
): Variants {
  return {
    hidden,
    visible,
    ...(exit && { exit }),
  };
}

// Utility function for responsive animations
export function createResponsiveVariants(
  mobile: Variants,
  desktop: Variants
): { mobile: Variants; desktop: Variants } {
  return { mobile, desktop };
}
