import { NextRequest, NextResponse } from "next/server";
import { writeFile, mkdir, readdir } from "fs/promises";
import { join } from "path";
import { v4 as uuidv4 } from "uuid";

interface CropConfig {
  targetWidth: number;
  targetHeight: number;
  aspectRatio?: number;
  quality?: number;
}

// Helper function to generate unique filename
const generateUniqueFilename = (originalName: string, contentHash: string): string => {
  const ext = originalName.substring(originalName.lastIndexOf('.'));
  const timestamp = Date.now();
  const uuid = uuidv4().substring(0, 8);
  return `${contentHash}_${timestamp}_${uuid}${ext}`;
};

// Helper function to check if file already exists by content hash
const checkDuplicateByHash = async (folderPath: string, contentHash: string): Promise<string | null> => {
  try {
    const files = await readdir(folderPath);
    const duplicateFile = files.find(file => file.startsWith(contentHash));
    return duplicateFile ? `/${folderPath.split('/').pop()}/${duplicateFile}` : null;
  } catch {
    return null;
  }
};

// Helper function to validate file type
const validateFileType = (file: File): boolean => {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  return allowedTypes.includes(file.type);
};

// Helper function to validate file size
const validateFileSize = (file: File, maxSizeMB: number = 10): boolean => {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  return file.size <= maxSizeBytes;
};

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const folder = formData.get('folder') as string || 'media';
    const contentHash = formData.get('contentHash') as string;
    const cropConfigStr = formData.get('cropConfig') as string;
    
    console.log(`📤 API: Upload request for ${file?.name} to ${folder}`);
    
    // Validation
    if (!file) {
      return NextResponse.json({ 
        success: false,
        error: "No file provided" 
      }, { status: 400 });
    }
    
    if (!validateFileType(file)) {
      return NextResponse.json({ 
        success: false,
        error: "Invalid file type. Only images are allowed." 
      }, { status: 400 });
    }
    
    if (!validateFileSize(file)) {
      return NextResponse.json({ 
        success: false,
        error: "File too large. Maximum size is 10MB." 
      }, { status: 400 });
    }
    
    // Create folder if it doesn't exist
    const folderPath = join(process.cwd(), "public", folder);
    try {
      await mkdir(folderPath, { recursive: true });
    } catch (error) {
      console.log(`📁 API: Folder ${folderPath} already exists or created`);
    }
    
    // Check for duplicate by content hash
    if (contentHash) {
      const existingFile = await checkDuplicateByHash(folderPath, contentHash);
      if (existingFile) {
        console.log(`🚫 API: Duplicate file detected by hash: ${existingFile}`);
        return NextResponse.json({
          success: true,
          data: {
            url: existingFile,
            originalName: file.name,
            size: file.size,
            id: existingFile.split('/').pop(),
            contentHash,
            createdAt: new Date().toISOString(),
            isDuplicate: true
          },
          message: "File already exists (duplicate detected)"
        });
      }
    }
    
    // Generate unique filename
    const uniqueFilename = generateUniqueFilename(file.name, contentHash || uuidv4().substring(0, 8));
    const filePath = join(folderPath, uniqueFilename);
    const publicUrl = `/${folder}/${uniqueFilename}`;
    
    console.log(`💾 API: Saving file as ${uniqueFilename}`);
    
    // Convert file to buffer and save
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    
    await writeFile(filePath, buffer);
    
    console.log(`✅ API: File saved successfully: ${publicUrl}`);
    
    // Return file info
    const fileInfo = {
      url: publicUrl,
      originalName: file.name,
      size: file.size,
      id: uniqueFilename,
      contentHash,
      createdAt: new Date().toISOString(),
      isDuplicate: false
    };
    
    return NextResponse.json({
      success: true,
      data: fileInfo,
      message: "File uploaded successfully"
    });
    
  } catch (error) {
    console.error("❌ API: Upload error:", error);
    return NextResponse.json({ 
      success: false,
      error: "Upload failed",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
