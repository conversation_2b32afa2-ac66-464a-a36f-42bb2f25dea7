"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  ChevronDown,
  Trash2,
  Download,
  Tag,
  Copy,
  Archive,
  Share,
  CheckSquare,
  Square,
} from 'lucide-react';

import { useMediaSelector } from '../../MediaSelectorProvider';
import { formatFileSize } from '../../utils/fileValidation';
import { buttonVariants } from '../../utils/animations';

export function BulkActions() {
  const { state, actions } = useMediaSelector();
  const { selectedItems, items, config } = state;
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const selectedFiles = items.filter(item => selectedItems.has(item.id));
  const totalSize = selectedFiles.reduce((sum, file) => sum + file.size, 0);
  const labels = config.labels!;

  // Handle bulk delete
  const handleBulkDelete = async () => {
    setIsProcessing(true);
    try {
      // Delete files one by one (you might want to implement a batch API endpoint)
      const deletePromises = selectedFiles.map(file => actions.deleteItem(file.id));
      await Promise.allSettled(deletePromises);
      actions.clearSelection();
    } catch (error) {
      console.error('Bulk delete failed:', error);
    } finally {
      setIsProcessing(false);
      setShowDeleteDialog(false);
    }
  };

  // Handle bulk download
  const handleBulkDownload = async () => {
    setIsProcessing(true);
    try {
      // Create a zip file or download files individually
      for (const file of selectedFiles) {
        const link = document.createElement('a');
        link.href = file.url;
        link.download = file.originalName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Add small delay between downloads
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    } catch (error) {
      console.error('Bulk download failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle copy URLs
  const handleCopyUrls = async () => {
    try {
      const urls = selectedFiles.map(file => file.url).join('\n');
      await navigator.clipboard.writeText(urls);
      // You might want to show a toast notification here
    } catch (error) {
      console.error('Failed to copy URLs:', error);
    }
  };

  // Toggle select all
  const handleToggleSelectAll = () => {
    if (selectedItems.size === items.length) {
      actions.clearSelection();
    } else {
      actions.selectAll();
    }
  };

  const isAllSelected = selectedItems.size === items.length && items.length > 0;

  return (
    <>
      <motion.div
        variants={buttonVariants}
        initial="idle"
        whileHover="hover"
        className="flex items-center gap-2"
      >
        {/* Selection info */}
        <Badge variant="secondary" className="px-3 py-1">
          {labels.selectedCount.replace('{count}', selectedItems.size.toString())}
          <span className="ml-2 text-xs text-muted-foreground">
            ({formatFileSize(totalSize)})
          </span>
        </Badge>

        {/* Select all toggle */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleToggleSelectAll}
          className="h-8"
        >
          {isAllSelected ? (
            <CheckSquare className="h-4 w-4 mr-1" />
          ) : (
            <Square className="h-4 w-4 mr-1" />
          )}
          {isAllSelected ? labels.deselectAll : labels.selectAll}
        </Button>

        {/* Actions dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              disabled={selectedItems.size === 0 || isProcessing}
              className="h-8"
            >
              Actions
              <ChevronDown className="h-4 w-4 ml-1" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem onClick={handleBulkDownload}>
              <Download className="h-4 w-4 mr-2" />
              Download Selected
            </DropdownMenuItem>
            
            <DropdownMenuItem onClick={handleCopyUrls}>
              <Copy className="h-4 w-4 mr-2" />
              Copy URLs
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            <DropdownMenuItem
              onClick={() => setShowDeleteDialog(true)}
              className="text-red-600 focus:text-red-600"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {labels.deleteSelected}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Clear selection */}
        <Button
          variant="ghost"
          size="sm"
          onClick={actions.clearSelection}
          className="h-8 text-muted-foreground hover:text-foreground"
        >
          Clear
        </Button>
      </motion.div>

      {/* Delete confirmation dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-red-600" />
              Delete Selected Files
            </AlertDialogTitle>
            <AlertDialogDescription className="space-y-2">
              <p>
                Are you sure you want to delete {selectedItems.size} selected file{selectedItems.size > 1 ? 's' : ''}?
              </p>
              <div className="bg-muted p-3 rounded-lg">
                <div className="text-sm font-medium mb-2">Files to be deleted:</div>
                <div className="space-y-1 max-h-32 overflow-y-auto">
                  {selectedFiles.slice(0, 5).map((file) => (
                    <div key={file.id} className="text-xs text-muted-foreground flex justify-between">
                      <span className="truncate">{file.originalName}</span>
                      <span>{formatFileSize(file.size)}</span>
                    </div>
                  ))}
                  {selectedFiles.length > 5 && (
                    <div className="text-xs text-muted-foreground">
                      ... and {selectedFiles.length - 5} more files
                    </div>
                  )}
                </div>
                <div className="text-sm font-medium mt-2 pt-2 border-t">
                  Total: {formatFileSize(totalSize)}
                </div>
              </div>
              <p className="text-red-600 font-medium">
                This action cannot be undone.
              </p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isProcessing}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleBulkDelete}
              disabled={isProcessing}
              className="bg-red-600 hover:bg-red-700"
            >
              {isProcessing ? (
                <>
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"
                  />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete {selectedItems.size} File{selectedItems.size > 1 ? 's' : ''}
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
