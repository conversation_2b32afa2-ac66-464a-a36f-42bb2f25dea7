"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2, Image, Grid, List } from 'lucide-react';

import { containerVariants, gridItemVariants } from '../../utils/animations';

interface LoadingStatesProps {
  view: 'grid' | 'list';
  count?: number;
}

export function LoadingStates({ view, count = 12 }: LoadingStatesProps) {
  if (view === 'grid') {
    return (
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="p-6"
      >
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
          {Array.from({ length: count }).map((_, index) => (
            <motion.div
              key={index}
              variants={gridItemVariants}
              custom={index}
              className="aspect-square"
            >
              <Card className="h-full overflow-hidden">
                <CardContent className="p-0 h-full">
                  <div className="relative h-full">
                    {/* Image skeleton */}
                    <Skeleton className="w-full h-full" />
                    
                    {/* Shimmer effect */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                      animate={{
                        x: ['-100%', '100%'],
                      }}
                      transition={{
                        duration: 1.5,
                        repeat: Infinity,
                        ease: 'easeInOut',
                        delay: index * 0.1,
                      }}
                    />
                    
                    {/* Loading icon */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                        className="p-3 bg-white/90 rounded-full shadow-lg"
                      >
                        <Image className="h-6 w-6 text-gray-400" />
                      </motion.div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.div>
    );
  }

  // List view loading
  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="p-6 space-y-3"
    >
      {Array.from({ length: count }).map((_, index) => (
        <motion.div
          key={index}
          variants={gridItemVariants}
          custom={index}
          className="flex items-center gap-4 p-4 bg-white dark:bg-gray-900 rounded-lg border"
        >
          {/* Checkbox skeleton */}
          <Skeleton className="h-4 w-4 rounded" />
          
          {/* Thumbnail skeleton */}
          <div className="relative">
            <Skeleton className="h-12 w-12 rounded-md" />
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-md"
              animate={{
                x: ['-100%', '100%'],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: 'easeInOut',
                delay: index * 0.1,
              }}
            />
          </div>
          
          {/* Content skeleton */}
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-3 w-1/2" />
          </div>
          
          {/* Tags skeleton */}
          <div className="flex gap-1">
            <Skeleton className="h-6 w-12 rounded-full" />
            <Skeleton className="h-6 w-16 rounded-full" />
          </div>
          
          {/* Actions skeleton */}
          <Skeleton className="h-8 w-8 rounded" />
        </motion.div>
      ))}
    </motion.div>
  );
}

// Specialized loading components
export function GalleryLoadingSkeleton() {
  return (
    <div className="p-6">
      {/* Header skeleton */}
      <div className="mb-6 space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-48" />
          <div className="flex gap-2">
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-8 w-20" />
          </div>
        </div>
        <Skeleton className="h-10 w-full" />
      </div>
      
      {/* Grid skeleton */}
      <LoadingStates view="grid" count={12} />
    </div>
  );
}

export function UploadLoadingSkeleton() {
  return (
    <div className="p-6 space-y-6">
      {/* Drop zone skeleton */}
      <Card className="border-2 border-dashed">
        <CardContent className="p-8 text-center">
          <div className="space-y-4">
            <div className="mx-auto w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              >
                <Loader2 className="h-8 w-8 text-gray-400" />
              </motion.div>
            </div>
            <div className="space-y-2">
              <Skeleton className="h-6 w-64 mx-auto" />
              <Skeleton className="h-4 w-48 mx-auto" />
            </div>
            <Skeleton className="h-10 w-32 mx-auto" />
          </div>
        </CardContent>
      </Card>
      
      {/* Progress skeleton */}
      <Card>
        <CardContent className="p-6 space-y-4">
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-5 w-16" />
          </div>
          <Skeleton className="h-3 w-full" />
          <div className="space-y-2">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <Skeleton className="h-4 w-4 rounded-full" />
                <div className="flex-1">
                  <Skeleton className="h-4 w-3/4 mb-1" />
                  <Skeleton className="h-1 w-full" />
                </div>
                <Skeleton className="h-5 w-12 rounded-full" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Inline loading spinner
export function InlineLoader({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
  };

  return (
    <motion.div
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
      className={`border-2 border-blue-500 border-t-transparent rounded-full ${sizeClasses[size]}`}
    />
  );
}

// Pulsing dot loader
export function PulsingDots() {
  return (
    <div className="flex space-x-1">
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className="w-2 h-2 bg-blue-500 rounded-full"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.7, 1, 0.7],
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            delay: index * 0.2,
          }}
        />
      ))}
    </div>
  );
}
