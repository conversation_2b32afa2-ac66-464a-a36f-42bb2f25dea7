"use client";

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import ReactCrop, { Crop, PixelCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import {
  X,
  Save,
  RotateCw,
  ZoomIn,
  ZoomOut,
  Crop as CropIcon,
  RefreshCw,
  Move,
  MousePointer,
} from 'lucide-react';

import type { MediaFile, CropData, CropConfig } from '../../types';
import { modalVariants } from '../../utils/animations';
import { useMediaSelector } from '../../MediaSelectorProvider';

interface CropEditorProps {
  item: MediaFile;
  onSave: (croppedFile: MediaFile) => void;
  onCancel: () => void;
}

export function CropEditor({ item, onSave, onCancel }: CropEditorProps) {
  const { state } = useMediaSelector();
  const { config } = state;
  const cropConfig = config.cropConfig;

  // React Image Crop state
  const [crop, setCrop] = useState<Crop>({
    unit: '%',
    x: 25,
    y: 25,
    width: 50,
    height: 50,
  });
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();

  // Zoom state
  const [zoom, setZoom] = useState(1);
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 });

  const [dimensions, setDimensions] = useState({
    width: cropConfig?.targetWidth || 800,
    height: cropConfig?.targetHeight || 600,
  });

  const [quality, setQuality] = useState(cropConfig?.quality || 90);
  const [isProcessing, setIsProcessing] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Calculate aspect ratio
  const aspectRatio = dimensions.width / dimensions.height;

  // Initialize crop area when image loads
  const onImageLoad = useCallback((e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;
    console.log('🖼️ Image loaded:', { width, height, aspectRatio });

    // Set initial crop area based on aspect ratio
    const imageAspectRatio = width / height;
    let cropWidth: number;
    let cropHeight: number;

    if (imageAspectRatio > aspectRatio) {
      // Image is wider than target aspect ratio
      cropHeight = 70; // 70% of image height
      cropWidth = (cropHeight * aspectRatio * width) / height;
    } else {
      // Image is taller than target aspect ratio
      cropWidth = 70; // 70% of image width
      cropHeight = (cropWidth * height) / (aspectRatio * width);
    }

    const x = (100 - cropWidth) / 2;
    const y = (100 - cropHeight) / 2;

    setCrop({
      unit: '%',
      x,
      y,
      width: cropWidth,
      height: cropHeight,
    });
  }, [aspectRatio]);

  // Reset crop to center
  const resetCrop = useCallback(() => {
    if (imageRef.current) {
      const img = imageRef.current;
      const imgAspectRatio = img.naturalWidth / img.naturalHeight;

      let cropWidth: number;
      let cropHeight: number;

      if (imgAspectRatio > aspectRatio) {
        cropHeight = 70;
        cropWidth = (cropHeight * aspectRatio * img.naturalWidth) / img.naturalHeight;
      } else {
        cropWidth = 70;
        cropHeight = (cropWidth * img.naturalHeight) / (aspectRatio * img.naturalWidth);
      }

      const x = (100 - cropWidth) / 2;
      const y = (100 - cropHeight) / 2;

      setCrop({
        unit: '%',
        x,
        y,
        width: cropWidth,
        height: cropHeight,
      });

      // Reset zoom and pan
      setZoom(1);
      setPanOffset({ x: 0, y: 0 });
    }
  }, [aspectRatio]);

  // Zoom functions
  const zoomIn = useCallback(() => {
    setZoom(prev => Math.min(prev * 1.2, 5));
  }, []);

  const zoomOut = useCallback(() => {
    setZoom(prev => Math.max(prev / 1.2, 0.1));
  }, []);

  const resetZoom = useCallback(() => {
    setZoom(1);
    setPanOffset({ x: 0, y: 0 });
  }, []);

  // Mouse wheel zoom
  const handleWheel = useCallback((e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    setZoom(prev => Math.max(0.1, Math.min(5, prev * delta)));
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case '=':
          case '+':
            e.preventDefault();
            zoomIn();
            break;
          case '-':
            e.preventDefault();
            zoomOut();
            break;
          case '0':
            e.preventDefault();
            resetZoom();
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [zoomIn, zoomOut, resetZoom]);

  // Handle save crop
  const handleSave = async () => {
    if (!completedCrop || !imageRef.current) {
      alert('Please select a crop area first.');
      return;
    }

    setIsProcessing(true);

    try {
      const image = imageRef.current;

      // Convert percentage crop to pixel crop if needed
      let pixelCrop = completedCrop;
      if (crop.unit === '%') {
        pixelCrop = {
          x: (crop.x * image.naturalWidth) / 100,
          y: (crop.y * image.naturalHeight) / 100,
          width: (crop.width * image.naturalWidth) / 100,
          height: (crop.height * image.naturalHeight) / 100,
          unit: 'px'
        };
      }

      console.log('🔄 Sending crop request:', {
        imageUrl: item.url,
        cropArea: pixelCrop,
        dimensions,
        quality
      });

      // Call crop API
      const response = await fetch('/api/media/crop', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: item.url,
          cropArea: {
            x: pixelCrop.x,
            y: pixelCrop.y,
            width: pixelCrop.width,
            height: pixelCrop.height,
          },
          folder: config.folder,
          config: {
            targetWidth: dimensions.width,
            targetHeight: dimensions.height,
            quality: quality,
          },
        }),
      });

      const result = await response.json();
      console.log('📥 Crop API response:', result);

      if (result.success && result.data) {
        // Create new media file object
        const croppedFile: MediaFile = {
          ...item,
          id: result.data.id,
          url: result.data.url,
          filename: result.data.originalName,
          originalName: `cropped_${item.originalName}`,
          size: result.data.size,
          createdAt: result.data.createdAt,
          isCropped: true,
          cropConfig: {
            targetWidth: dimensions.width,
            targetHeight: dimensions.height,
            quality: quality,
          },
          originalImage: item.url,
        };

        console.log('✅ Crop successful, calling onSave with:', croppedFile);
        onSave(croppedFile);
      } else {
        throw new Error(result.error || 'Crop failed');
      }
    } catch (error) {
      console.error('❌ Crop error:', error);
      alert(`Failed to crop image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog open onOpenChange={onCancel}>
      <DialogContent className="max-w-6xl h-[90vh] p-0 overflow-hidden">
        <motion.div
          variants={modalVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="h-full flex flex-col"
        >
          {/* Header */}
          <DialogHeader className="px-6 py-4 border-b bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950">
            <div className="flex items-center justify-between">
              <div>
                <DialogTitle className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  <CropIcon className="h-5 w-5 inline mr-2" />
                  Crop Image
                </DialogTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  {item.originalName}
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onCancel}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </DialogHeader>

          {/* Content */}
          <div className="flex-1 flex overflow-hidden">
            {/* Preview Area */}
            <div className="flex-1 bg-gray-50 dark:bg-gray-900 flex flex-col">
              {/* Zoom Controls */}
              <div className="flex items-center justify-center gap-2 p-4 border-b bg-white dark:bg-gray-950">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={zoomOut}
                  disabled={zoom <= 0.1}
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <span className="text-sm font-mono min-w-[60px] text-center">
                  {Math.round(zoom * 100)}%
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={zoomIn}
                  disabled={zoom >= 5}
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetZoom}
                  className="ml-2"
                >
                  <Move className="h-4 w-4 mr-1" />
                  Reset
                </Button>
              </div>

              {/* Crop Area */}
              <div
                ref={containerRef}
                className="flex-1 flex items-center justify-center p-6 overflow-hidden"
                style={{
                  transform: `scale(${zoom}) translate(${panOffset.x}px, ${panOffset.y}px)`,
                  transformOrigin: 'center center',
                  transition: 'transform 0.2s ease-out'
                }}
                onWheel={handleWheel}
              >
                <ReactCrop
                  crop={crop}
                  onChange={(_, percentCrop) => setCrop(percentCrop)}
                  onComplete={(c) => setCompletedCrop(c)}
                  aspect={aspectRatio}
                  minWidth={50}
                  minHeight={50}
                  className="max-w-full max-h-full"
                >
                  <img
                    ref={imageRef}
                    src={item.url}
                    alt={item.alt || item.originalName}
                    className="max-w-full max-h-full object-contain"
                    onLoad={onImageLoad}
                    style={{ maxWidth: '100%', maxHeight: '100%' }}
                  />
                </ReactCrop>
              </div>

              {/* Instructions */}
              <div className="p-3 bg-blue-50 dark:bg-blue-950 border-t text-center">
                <p className="text-xs text-blue-700 dark:text-blue-300 flex items-center justify-center gap-2">
                  <MousePointer className="h-3 w-3" />
                  Drag corners to resize • Drag center to move • Mouse wheel to zoom • Ctrl+/- for zoom
                </p>
              </div>
            </div>

            {/* Controls Panel */}
            <div className="w-80 border-l bg-white dark:bg-gray-950 flex flex-col p-6 space-y-6">
              {/* Crop Info */}
              <div className="space-y-3">
                <h3 className="font-semibold text-sm flex items-center gap-2">
                  <CropIcon className="h-4 w-4" />
                  Crop Area
                </h3>
                {completedCrop && (
                  <div className="text-xs text-muted-foreground space-y-1 bg-gray-50 dark:bg-gray-900 p-3 rounded">
                    <p>X: {Math.round(completedCrop.x)}px</p>
                    <p>Y: {Math.round(completedCrop.y)}px</p>
                    <p>Width: {Math.round(completedCrop.width)}px</p>
                    <p>Height: {Math.round(completedCrop.height)}px</p>
                  </div>
                )}
              </div>

              {/* Dimensions */}
              <div className="space-y-4">
                <h3 className="font-semibold text-sm">Output Dimensions</h3>
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor="width" className="text-xs">Width</Label>
                    <Input
                      id="width"
                      type="number"
                      value={dimensions.width}
                      onChange={(e) => setDimensions(prev => ({
                        ...prev,
                        width: parseInt(e.target.value) || 0
                      }))}
                      className="h-8"
                    />
                  </div>
                  <div>
                    <Label htmlFor="height" className="text-xs">Height</Label>
                    <Input
                      id="height"
                      type="number"
                      value={dimensions.height}
                      onChange={(e) => setDimensions(prev => ({
                        ...prev,
                        height: parseInt(e.target.value) || 0
                      }))}
                      className="h-8"
                    />
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  Aspect Ratio: {aspectRatio.toFixed(2)}
                </p>
              </div>

              {/* Quality */}
              <div className="space-y-3">
                <Label className="text-xs">Quality: {quality}%</Label>
                <Slider
                  value={[quality]}
                  onValueChange={(value) => setQuality(value[0])}
                  max={100}
                  min={10}
                  step={5}
                  className="w-full"
                />
              </div>

              {/* Actions */}
              <div className="space-y-3">
                <Button
                  variant="outline"
                  onClick={resetCrop}
                  className="w-full h-8 text-xs"
                >
                  <RefreshCw className="h-3 w-3 mr-2" />
                  Reset Crop
                </Button>
                <Button
                  variant="outline"
                  onClick={resetZoom}
                  className="w-full h-8 text-xs"
                >
                  <Move className="h-3 w-3 mr-2" />
                  Reset Zoom
                </Button>
              </div>

              {/* Info */}
              <div className="text-xs text-muted-foreground space-y-1">
                <p>Original: {item.originalName}</p>
                <p>Size: {Math.round((item.size || 0) / 1024)} KB</p>
                <p>Type: {item.mimeType}</p>
                <p>Zoom: {Math.round(zoom * 100)}%</p>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="px-6 py-4 border-t bg-muted/30 flex gap-3">
            <Button variant="outline" onClick={onCancel} className="flex-1">
              Cancel
            </Button>
            <Button 
              onClick={handleSave} 
              disabled={isProcessing}
              className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Processing...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Cropped Image
                </>
              )}
            </Button>
          </div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
