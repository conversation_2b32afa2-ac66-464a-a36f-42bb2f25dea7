"use client";

import React, { useState, useRef, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import {
  X,
  Save,
  RotateCw,
  ZoomIn,
  ZoomOut,
  Crop,
  RefreshCw,
} from 'lucide-react';

import type { MediaFile, CropData, CropConfig } from '../../types';
import { modalVariants } from '../../utils/animations';
import { useMediaSelector } from '../../MediaSelectorProvider';

interface CropEditorProps {
  item: MediaFile;
  onSave: (croppedFile: MediaFile) => void;
  onCancel: () => void;
}

export function CropEditor({ item, onSave, onCancel }: CropEditorProps) {
  const { state } = useMediaSelector();
  const { config } = state;
  const cropConfig = config.cropConfig;
  
  const [cropData, setCropData] = useState<CropData>({
    x: 0,
    y: 0,
    width: 200,
    height: 200,
    zoom: 1,
    rotation: 0,
  });
  
  const [dimensions, setDimensions] = useState({
    width: cropConfig?.targetWidth || 800,
    height: cropConfig?.targetHeight || 600,
  });
  
  const [quality, setQuality] = useState(cropConfig?.quality || 90);
  const [isProcessing, setIsProcessing] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Calculate aspect ratio
  const aspectRatio = dimensions.width / dimensions.height;

  // Handle crop area change
  const handleCropChange = useCallback((newCropData: Partial<CropData>) => {
    setCropData(prev => ({ ...prev, ...newCropData }));
  }, []);

  // Reset crop to center
  const resetCrop = useCallback(() => {
    if (imageRef.current) {
      const img = imageRef.current;
      const imgAspectRatio = img.naturalWidth / img.naturalHeight;
      
      let cropWidth: number;
      let cropHeight: number;
      
      if (imgAspectRatio > aspectRatio) {
        cropHeight = img.naturalHeight;
        cropWidth = cropHeight * aspectRatio;
      } else {
        cropWidth = img.naturalWidth;
        cropHeight = cropWidth / aspectRatio;
      }
      
      setCropData({
        x: (img.naturalWidth - cropWidth) / 2,
        y: (img.naturalHeight - cropHeight) / 2,
        width: cropWidth,
        height: cropHeight,
        zoom: 1,
        rotation: 0,
      });
    }
  }, [aspectRatio]);

  // Handle save crop
  const handleSave = async () => {
    setIsProcessing(true);
    
    try {
      // Call crop API
      const response = await fetch('/api/media/crop', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: item.url,
          cropArea: cropData,
          folder: config.folder,
          config: {
            targetWidth: dimensions.width,
            targetHeight: dimensions.height,
            quality: quality,
          },
        }),
      });

      const result = await response.json();
      
      if (result.success && result.data) {
        // Create new media file object
        const croppedFile: MediaFile = {
          ...item,
          id: result.data.id,
          url: result.data.url,
          filename: result.data.originalName,
          originalName: `cropped_${item.originalName}`,
          size: result.data.size,
          createdAt: result.data.createdAt,
          isCropped: true,
          cropConfig: {
            targetWidth: dimensions.width,
            targetHeight: dimensions.height,
            quality: quality,
          },
          originalImage: item.url,
        };
        
        onSave(croppedFile);
      } else {
        throw new Error(result.error || 'Crop failed');
      }
    } catch (error) {
      console.error('Crop error:', error);
      alert('Failed to crop image. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog open onOpenChange={onCancel}>
      <DialogContent className="max-w-6xl h-[90vh] p-0 overflow-hidden">
        <motion.div
          variants={modalVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="h-full flex flex-col"
        >
          {/* Header */}
          <DialogHeader className="px-6 py-4 border-b bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950">
            <div className="flex items-center justify-between">
              <div>
                <DialogTitle className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  <Crop className="h-5 w-5 inline mr-2" />
                  Crop Image
                </DialogTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  {item.originalName}
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onCancel}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </DialogHeader>

          {/* Content */}
          <div className="flex-1 flex overflow-hidden">
            {/* Preview Area */}
            <div className="flex-1 bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-6">
              <div className="relative max-w-full max-h-full">
                <img
                  ref={imageRef}
                  src={item.url}
                  alt={item.alt || item.originalName}
                  className="max-w-full max-h-full object-contain"
                  onLoad={resetCrop}
                />
                
                {/* Crop overlay would go here in a real implementation */}
                <div className="absolute inset-0 border-2 border-blue-500 border-dashed opacity-50 pointer-events-none" />
              </div>
            </div>

            {/* Controls Panel */}
            <div className="w-80 border-l bg-white dark:bg-gray-950 flex flex-col p-6 space-y-6">
              {/* Dimensions */}
              <div className="space-y-4">
                <h3 className="font-semibold text-sm">Output Dimensions</h3>
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor="width" className="text-xs">Width</Label>
                    <Input
                      id="width"
                      type="number"
                      value={dimensions.width}
                      onChange={(e) => setDimensions(prev => ({ 
                        ...prev, 
                        width: parseInt(e.target.value) || 0 
                      }))}
                      className="h-8"
                    />
                  </div>
                  <div>
                    <Label htmlFor="height" className="text-xs">Height</Label>
                    <Input
                      id="height"
                      type="number"
                      value={dimensions.height}
                      onChange={(e) => setDimensions(prev => ({ 
                        ...prev, 
                        height: parseInt(e.target.value) || 0 
                      }))}
                      className="h-8"
                    />
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  Aspect Ratio: {aspectRatio.toFixed(2)}
                </p>
              </div>

              {/* Quality */}
              <div className="space-y-3">
                <Label className="text-xs">Quality: {quality}%</Label>
                <Slider
                  value={[quality]}
                  onValueChange={(value) => setQuality(value[0])}
                  max={100}
                  min={10}
                  step={5}
                  className="w-full"
                />
              </div>

              {/* Actions */}
              <div className="space-y-3">
                <Button
                  variant="outline"
                  onClick={resetCrop}
                  className="w-full h-8 text-xs"
                >
                  <RefreshCw className="h-3 w-3 mr-2" />
                  Reset Crop
                </Button>
              </div>

              {/* Info */}
              <div className="text-xs text-muted-foreground space-y-1">
                <p>Original: {item.originalName}</p>
                <p>Size: {Math.round((item.size || 0) / 1024)} KB</p>
                <p>Type: {item.mimeType}</p>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="px-6 py-4 border-t bg-muted/30 flex gap-3">
            <Button variant="outline" onClick={onCancel} className="flex-1">
              Cancel
            </Button>
            <Button 
              onClick={handleSave} 
              disabled={isProcessing}
              className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Processing...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Cropped Image
                </>
              )}
            </Button>
          </div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
