import type { FileValidationResult } from '../types';

// File type validation
export function validateFileType(file: File, acceptedTypes: string[]): boolean {
  if (acceptedTypes.includes('*/*')) return true;
  
  return acceptedTypes.some(type => {
    if (type.endsWith('/*')) {
      const category = type.split('/')[0];
      return file.type.startsWith(category + '/');
    }
    return file.type === type;
  });
}

// File size validation
export function validateFileSize(file: File, maxSize: number): boolean {
  return file.size <= maxSize;
}

// File name validation
export function validateFileName(filename: string): boolean {
  // Check for invalid characters
  const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
  if (invalidChars.test(filename)) return false;
  
  // Check for reserved names (Windows)
  const reservedNames = /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\.|$)/i;
  if (reservedNames.test(filename)) return false;
  
  // Check length
  if (filename.length > 255) return false;
  
  return true;
}

// Comprehensive file validation
export function validateFile(
  file: File,
  acceptedTypes: string[],
  maxSize: number
): FileValidationResult {
  const errors: string[] = [];
  
  // Type validation
  if (!validateFileType(file, acceptedTypes)) {
    errors.push(`File type "${file.type}" is not allowed. Accepted types: ${acceptedTypes.join(', ')}`);
  }
  
  // Size validation
  if (!validateFileSize(file, maxSize)) {
    errors.push(`File size (${formatFileSize(file.size)}) exceeds maximum allowed size (${formatFileSize(maxSize)})`);
  }
  
  // Name validation
  if (!validateFileName(file.name)) {
    errors.push('File name contains invalid characters or is too long');
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
}

// Batch file validation
export function validateFiles(
  files: File[],
  acceptedTypes: string[],
  maxSize: number,
  maxFiles?: number
): { valid: boolean; errors: string[]; fileErrors: Record<string, string[]> } {
  const errors: string[] = [];
  const fileErrors: Record<string, string[]> = {};
  
  // Check file count
  if (maxFiles && files.length > maxFiles) {
    errors.push(`Too many files. Maximum allowed: ${maxFiles}, provided: ${files.length}`);
  }
  
  // Validate each file
  files.forEach((file, index) => {
    const result = validateFile(file, acceptedTypes, maxSize);
    if (!result.valid) {
      fileErrors[`file-${index}`] = result.errors;
    }
  });
  
  return {
    valid: errors.length === 0 && Object.keys(fileErrors).length === 0,
    errors,
    fileErrors,
  };
}

// File size formatting
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Get file extension
export function getFileExtension(filename: string): string {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
}

// Get file type category
export function getFileCategory(mimeType: string): 'image' | 'video' | 'audio' | 'document' | 'other' {
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType.startsWith('video/')) return 'video';
  if (mimeType.startsWith('audio/')) return 'audio';
  if (mimeType.startsWith('application/') || mimeType.startsWith('text/')) return 'document';
  return 'other';
}

// Check if file is an image
export function isImage(file: File): boolean {
  return file.type.startsWith('image/');
}

// Check if file is a video
export function isVideo(file: File): boolean {
  return file.type.startsWith('video/');
}

// Generate unique filename
export function generateUniqueFilename(originalName: string, hash?: string): string {
  const extension = getFileExtension(originalName);
  const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 8);
  
  if (hash) {
    return `${nameWithoutExt}_${hash}.${extension}`;
  }
  
  return `${nameWithoutExt}_${timestamp}_${randomSuffix}.${extension}`;
}

// Sanitize filename
export function sanitizeFilename(filename: string): string {
  // Replace invalid characters with underscores
  let sanitized = filename.replace(/[<>:"/\\|?*\x00-\x1f]/g, '_');
  
  // Remove multiple consecutive underscores
  sanitized = sanitized.replace(/_+/g, '_');
  
  // Remove leading/trailing underscores and dots
  sanitized = sanitized.replace(/^[._]+|[._]+$/g, '');
  
  // Ensure it's not empty
  if (!sanitized) {
    sanitized = 'file';
  }
  
  // Truncate if too long
  if (sanitized.length > 255) {
    const extension = getFileExtension(sanitized);
    const nameWithoutExt = sanitized.substring(0, 255 - extension.length - 1);
    sanitized = `${nameWithoutExt}.${extension}`;
  }
  
  return sanitized;
}

// Check for duplicate files
export function isDuplicateFile(file: File, existingFiles: File[]): boolean {
  return existingFiles.some(existing => 
    existing.name === file.name && 
    existing.size === file.size && 
    existing.lastModified === file.lastModified
  );
}

// Calculate file hash (for duplicate detection)
export async function calculateFileHash(file: File): Promise<string> {
  const buffer = await file.arrayBuffer();
  const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

// Image dimension validation
export function validateImageDimensions(
  file: File,
  minWidth?: number,
  minHeight?: number,
  maxWidth?: number,
  maxHeight?: number
): Promise<FileValidationResult> {
  return new Promise((resolve) => {
    if (!isImage(file)) {
      resolve({ valid: true, errors: [] });
      return;
    }
    
    const img = new Image();
    const url = URL.createObjectURL(file);
    
    img.onload = () => {
      URL.revokeObjectURL(url);
      const errors: string[] = [];
      
      if (minWidth && img.width < minWidth) {
        errors.push(`Image width (${img.width}px) is less than minimum required (${minWidth}px)`);
      }
      
      if (minHeight && img.height < minHeight) {
        errors.push(`Image height (${img.height}px) is less than minimum required (${minHeight}px)`);
      }
      
      if (maxWidth && img.width > maxWidth) {
        errors.push(`Image width (${img.width}px) exceeds maximum allowed (${maxWidth}px)`);
      }
      
      if (maxHeight && img.height > maxHeight) {
        errors.push(`Image height (${img.height}px) exceeds maximum allowed (${maxHeight}px)`);
      }
      
      resolve({
        valid: errors.length === 0,
        errors,
      });
    };
    
    img.onerror = () => {
      URL.revokeObjectURL(url);
      resolve({
        valid: false,
        errors: ['Invalid image file'],
      });
    };
    
    img.src = url;
  });
}
