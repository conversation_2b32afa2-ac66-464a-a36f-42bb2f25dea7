// API response and request types

import type { MediaFile } from './index';

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface MediaListResponse extends ApiResponse {
  data: MediaFile[];
  pagination?: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

export interface MediaUploadResponse extends ApiResponse {
  data: MediaFile;
}

export interface MediaDeleteResponse extends ApiResponse {
  data: {
    id: string;
    deleted: boolean;
  };
}

export interface MediaUpdateResponse extends ApiResponse {
  data: MediaFile;
}

// API Request types
export interface MediaListRequest {
  folder?: string;
  page?: number;
  pageSize?: number;
  search?: string;
  type?: string;
  sortBy?: 'name' | 'size' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

export interface MediaUploadRequest {
  file: File;
  folder: string;
  alt?: string;
  caption?: string;
  tags?: string[];
  cropConfig?: {
    x: number;
    y: number;
    width: number;
    height: number;
    targetWidth?: number;
    targetHeight?: number;
    quality?: number;
  };
}

export interface MediaUpdateRequest {
  id: string;
  alt?: string;
  caption?: string;
  tags?: string[];
}

export interface MediaDeleteRequest {
  id: string;
  folder?: string;
}

export interface BatchDeleteRequest {
  ids: string[];
  folder?: string;
}

// Error types
export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

export interface ValidationError extends ApiError {
  code: 'VALIDATION_ERROR';
  details: {
    field: string;
    message: string;
  }[];
}

export interface FileError extends ApiError {
  code: 'FILE_ERROR';
  details: {
    filename: string;
    size: number;
    type: string;
    reason: 'TOO_LARGE' | 'INVALID_TYPE' | 'CORRUPTED' | 'DUPLICATE';
  };
}

export interface NetworkError extends ApiError {
  code: 'NETWORK_ERROR';
  details: {
    status: number;
    statusText: string;
    url: string;
  };
}

// Upload progress types
export interface UploadProgress {
  fileId: string;
  filename: string;
  loaded: number;
  total: number;
  percentage: number;
  speed: number; // bytes per second
  timeRemaining: number; // seconds
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
}

// Batch operation types
export interface BatchOperationProgress {
  operationId: string;
  type: 'upload' | 'delete' | 'update';
  total: number;
  completed: number;
  failed: number;
  percentage: number;
  status: 'pending' | 'running' | 'completed' | 'error';
  results: Array<{
    id: string;
    success: boolean;
    error?: string;
  }>;
}

// WebSocket message types for real-time updates
export interface WebSocketMessage {
  type: 'upload_progress' | 'upload_complete' | 'upload_error' | 'media_updated' | 'media_deleted';
  payload: any;
}

export interface UploadProgressMessage extends WebSocketMessage {
  type: 'upload_progress';
  payload: UploadProgress;
}

export interface UploadCompleteMessage extends WebSocketMessage {
  type: 'upload_complete';
  payload: {
    fileId: string;
    media: MediaFile;
  };
}

export interface UploadErrorMessage extends WebSocketMessage {
  type: 'upload_error';
  payload: {
    fileId: string;
    error: string;
  };
}

export interface MediaUpdatedMessage extends WebSocketMessage {
  type: 'media_updated';
  payload: MediaFile;
}

export interface MediaDeletedMessage extends WebSocketMessage {
  type: 'media_deleted';
  payload: {
    id: string;
  };
}
