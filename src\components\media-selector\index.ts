// Main exports for the Media Selector component system

// Core components
export { MediaSelector } from './MediaSelector';
export { MediaSelectorProvider, useMediaSelector } from './MediaSelectorProvider';

// Sub-components
export { MediaGrid } from './components/Gallery/MediaGrid';
export { MediaItem } from './components/Gallery/MediaItem';
export { SearchFilter } from './components/Gallery/SearchFilter';
export { BulkActions } from './components/Gallery/BulkActions';

export { DropZone } from './components/Upload/DropZone';
export { ProgressTracker } from './components/Upload/ProgressTracker';

export { ImageEditor } from './components/Editor/ImageEditor';

export { LoadingStates, GalleryLoadingSkeleton, UploadLoadingSkeleton, InlineLoader, PulsingDots } from './components/UI/LoadingStates';
export { EmptyStates, UploadEmptyState, FilterEmptyState } from './components/UI/EmptyStates';

// Types
export type {
  MediaFile,
  MediaSelectorConfig,
  MediaSelectorProps,
  MediaSelectorState,
  MediaSelectorAction,
  CropConfig,
  CropData,
  MediaFilters,
  MediaSelectorLabels,
  UseMediaSelectorReturn,
  MediaGridProps,
  DropZoneProps,
  FileValidationResult,
  UploadResult,
} from './types';

export type {
  ImageFile,
  VideoFile,
  DocumentFile,
  MediaSearchResult,
  MediaSearchQuery,
  ProcessingStatus,
  MediaProcessingJob,
  BatchOperation,
  MediaCollection,
} from './types/media';

export type {
  ApiResponse,
  MediaListResponse,
  MediaUploadResponse,
  MediaDeleteResponse,
  MediaUpdateResponse,
  MediaListRequest,
  MediaUploadRequest,
  MediaUpdateRequest,
  MediaDeleteRequest,
  BatchDeleteRequest,
  UploadProgress,
  BatchOperationProgress,
  ApiError,
  ValidationError,
  FileError,
  NetworkError,
} from './types/api';

// Configuration presets
export {
  DEFAULT_CONFIG,
  DEFAULT_LABELS,
  TURKISH_LABELS,
  PROJECT_IMAGES_CONFIG,
  BLOG_IMAGES_CONFIG,
  PRODUCT_IMAGES_CONFIG,
  DOCUMENT_CONFIG,
  mergeConfig,
  validateConfig,
} from './types/config';

// Utilities
export {
  validateFile,
  validateFiles,
  validateFileType,
  validateFileSize,
  validateFileName,
  validateImageDimensions,
  formatFileSize,
  getFileExtension,
  getFileCategory,
  isImage,
  isVideo,
  generateUniqueFilename,
  sanitizeFilename,
  isDuplicateFile,
  calculateFileHash,
} from './utils/fileValidation';

export {
  loadImage,
  getImageDimensions,
  cropImage,
  resizeImage,
  compressImage,
  generateThumbnail,
  applyFilter,
  convertImageFormat,
  calculateCropArea,
  getImageExif,
} from './utils/imageProcessing';

export {
  MediaApiClient,
  mediaApi,
  UploadQueue,
  isSuccessResponse,
  getErrorMessage,
  retryRequest,
} from './utils/apiHelpers';

export {
  containerVariants,
  gridItemVariants,
  modalVariants,
  backdropVariants,
  tabVariants,
  progressVariants,
  buttonVariants,
  dropZoneVariants,
  searchVariants,
  spinnerVariants,
  toastVariants,
  slideVariants,
  fadeVariants,
  scaleVariants,
  staggerContainer,
  springConfig,
  customEasing,
  ANIMATION_DURATION,
  createCustomVariants,
  createResponsiveVariants,
} from './utils/animations';

// Default export
export default MediaSelector;
