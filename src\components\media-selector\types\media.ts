// Extended media file types for specific use cases

export interface ImageFile extends MediaFile {
  mimeType: `image/${string}`;
  dimensions: {
    width: number;
    height: number;
  };
  exif?: {
    camera?: string;
    lens?: string;
    iso?: number;
    aperture?: string;
    shutterSpeed?: string;
    focalLength?: string;
    dateTaken?: string;
    location?: {
      latitude: number;
      longitude: number;
    };
  };
}

export interface VideoFile extends MediaFile {
  mimeType: `video/${string}`;
  duration?: number;
  dimensions?: {
    width: number;
    height: number;
  };
  bitrate?: number;
  framerate?: number;
  codec?: string;
}

export interface DocumentFile extends MediaFile {
  mimeType: `application/${string}` | `text/${string}`;
  pageCount?: number;
  wordCount?: number;
  language?: string;
}

// Media processing states
export type ProcessingStatus = 'pending' | 'processing' | 'completed' | 'failed';

export interface MediaProcessingJob {
  id: string;
  fileId: string;
  type: 'thumbnail' | 'resize' | 'crop' | 'compress' | 'convert';
  status: ProcessingStatus;
  progress: number;
  startedAt: string;
  completedAt?: string;
  error?: string;
  result?: {
    url: string;
    size: number;
    dimensions?: {
      width: number;
      height: number;
    };
  };
}

// Batch operation types
export interface BatchOperation {
  id: string;
  type: 'delete' | 'move' | 'copy' | 'tag' | 'metadata';
  fileIds: string[];
  status: ProcessingStatus;
  progress: number;
  results: Record<string, { success: boolean; error?: string }>;
}

// Media collection/album types
export interface MediaCollection {
  id: string;
  name: string;
  description?: string;
  coverImage?: string;
  fileIds: string[];
  createdAt: string;
  updatedAt: string;
  tags?: string[];
  isPublic: boolean;
}

// Search and filter types
export interface MediaSearchResult {
  items: MediaFile[];
  total: number;
  page: number;
  pageSize: number;
  facets: {
    types: Record<string, number>;
    sizes: Record<string, number>;
    dates: Record<string, number>;
    tags: Record<string, number>;
  };
}

export interface MediaSearchQuery {
  query?: string;
  filters: MediaFilters;
  sort: {
    field: 'name' | 'size' | 'createdAt' | 'updatedAt';
    direction: 'asc' | 'desc';
  };
  page: number;
  pageSize: number;
}

// Import from main types file
import type { MediaFile, MediaFilters } from './index';
