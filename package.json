{"name": "baki<PERSON><PERSON>", "version": "1.9.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:full": "npm run clean:debug && next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "clean:debug": "tsx scripts/cleanup-debug-logs.ts", "db:seed": "npx prisma db seed", "db:seed-technicians": "tsx scripts/seed-technicians-expertise.ts", "db:migrate": "npx prisma migrate dev", "db:reset": "npx prisma migrate reset", "db:generate": "npx prisma generate", "version:update": "node scripts/update-version.js", "version:bump": "node scripts/update-version.js", "performance:audit": "npm audit && npm run build:analyze", "docker:deploy": "chmod +x scripts/docker-deploy.sh && ./scripts/docker-deploy.sh", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:ml-logs": "docker logs bakimonarim_ml_service -f", "test:appointment": "tsx scripts/test-appointment-workflow.ts", "test:appointment:completion": "tsx scripts/test-appointment-completion.ts", "docker:build": "docker build -t b<PERSON><PERSON><PERSON> .", "docker:run": "docker run -p 3000:3000 bakimonarim"}, "dependencies": {"@auth/core": "^0.40.0", "@auth/prisma-adapter": "^2.10.0", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.11.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.81.2", "@types/ioredis": "^4.28.10", "@types/uuid": "^10.0.0", "@uiw/react-md-editor": "^4.0.7", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "framer-motion": "^12.23.3", "ioredis": "^5.6.1", "lucide-react": "^0.515.0", "next": "15.3.3", "next-auth": "^5.0.0-beta.28", "prisma": "^6.11.0", "react": "^19.0.0", "react-big-calendar": "^1.19.4", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-easy-crop": "^5.5.0", "react-hook-form": "^7.58.0", "react-image-crop": "^11.0.10", "recharts": "^2.15.3", "sharp": "^0.34.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "zod": "^3.25.65", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-big-calendar": "^1.16.2", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5"}}