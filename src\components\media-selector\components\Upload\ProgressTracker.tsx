"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Upload,
  CheckCircle,
  AlertCircle,
  Clock,
  HardDrive,
  Zap,
  TrendingUp,
} from 'lucide-react';

import { useMediaSelector } from '../../MediaSelectorProvider';
import { formatFileSize } from '../../utils/fileValidation';
import { containerVariants, gridItemVariants, progressVariants } from '../../utils/animations';

export function ProgressTracker() {
  const { state } = useMediaSelector();
  const { uploadProgress, uploading } = state;

  const progressEntries = Object.entries(uploadProgress);
  const totalProgress = progressEntries.length > 0
    ? progressEntries.reduce((sum, [, progress]) => sum + progress, 0) / progressEntries.length
    : 0;

  const completedUploads = progressEntries.filter(([, progress]) => progress === 100).length;
  const activeUploads = progressEntries.filter(([, progress]) => progress > 0 && progress < 100).length;
  const pendingUploads = progressEntries.filter(([, progress]) => progress === 0).length;

  if (!uploading && progressEntries.length === 0) {
    return null;
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-4"
    >
      {/* KPI Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <motion.div variants={gridItemVariants}>
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 border-blue-200 dark:border-blue-800">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-700 dark:text-blue-300">
                    Overall Progress
                  </p>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {Math.round(totalProgress)}%
                  </p>
                </div>
                <div className="p-2 bg-blue-500 rounded-full">
                  <TrendingUp className="h-4 w-4 text-white" />
                </div>
              </div>
              <Progress 
                value={totalProgress} 
                className="mt-2 h-2"
              />
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={gridItemVariants}>
          <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 border-green-200 dark:border-green-800">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-700 dark:text-green-300">
                    Completed
                  </p>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {completedUploads}
                  </p>
                </div>
                <div className="p-2 bg-green-500 rounded-full">
                  <CheckCircle className="h-4 w-4 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={gridItemVariants}>
          <Card className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900 border-orange-200 dark:border-orange-800">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-orange-700 dark:text-orange-300">
                    Active
                  </p>
                  <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {activeUploads}
                  </p>
                </div>
                <div className="p-2 bg-orange-500 rounded-full">
                  <Zap className="h-4 w-4 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={gridItemVariants}>
          <Card className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950 dark:to-gray-900 border-gray-200 dark:border-gray-800">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Pending
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {pendingUploads}
                  </p>
                </div>
                <div className="p-2 bg-gray-500 rounded-full">
                  <Clock className="h-4 w-4 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Detailed Progress */}
      <motion.div variants={gridItemVariants}>
        <Card className="bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Upload className="h-5 w-5" />
              Upload Progress
              <Badge variant="secondary" className="ml-auto">
                {progressEntries.length} files
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {/* Overall progress bar */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="font-medium">Overall Progress</span>
                <span className="text-muted-foreground">
                  {completedUploads} of {progressEntries.length} completed
                </span>
              </div>
              <motion.div
                variants={progressVariants}
                className="relative"
              >
                <Progress value={totalProgress} className="h-3" />
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full"
                  animate={{
                    opacity: [0.5, 1, 0.5],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
              </motion.div>
            </div>

            {/* Individual file progress */}
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {progressEntries.map(([fileId, progress]) => {
                const status = progress === 100 ? 'completed' : progress > 0 ? 'uploading' : 'pending';
                
                return (
                  <motion.div
                    key={fileId}
                    variants={gridItemVariants}
                    className="flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-800 rounded-lg"
                  >
                    {/* Status icon */}
                    <div className="flex-shrink-0">
                      {status === 'completed' && (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      )}
                      {status === 'uploading' && (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"
                        />
                      )}
                      {status === 'pending' && (
                        <Clock className="h-4 w-4 text-gray-400" />
                      )}
                    </div>

                    {/* File info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-sm font-medium truncate">
                          File {fileId.split('-').pop()}
                        </span>
                        <Badge
                          variant={
                            status === 'completed' ? 'default' :
                            status === 'uploading' ? 'secondary' : 'outline'
                          }
                          className="text-xs"
                        >
                          {status === 'completed' ? 'Done' :
                           status === 'uploading' ? `${Math.round(progress)}%` : 'Waiting'}
                        </Badge>
                      </div>
                      
                      {status !== 'pending' && (
                        <Progress value={progress} className="h-1" />
                      )}
                    </div>
                  </motion.div>
                );
              })}
            </div>

            {/* Upload stats */}
            <div className="flex justify-between items-center pt-2 border-t text-sm text-muted-foreground">
              <span>
                {completedUploads} completed, {activeUploads} active, {pendingUploads} pending
              </span>
              <span className="flex items-center gap-1">
                <HardDrive className="h-3 w-3" />
                Processing...
              </span>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
