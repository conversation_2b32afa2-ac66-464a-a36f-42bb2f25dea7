import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export interface MediaFile {
  url: string;
  originalName: string;
  size: number;
  alt?: string;
  id: string;
  contentHash?: string;
  createdAt: string;
  dimensions?: {
    width: number;
    height: number;
  };
}

export interface CropConfig {
  targetWidth: number;
  targetHeight: number;
  aspectRatio?: number;
  minZoom?: number;
  maxZoom?: number;
  quality?: number;
}

interface MediaState {
  // State
  items: MediaFile[];
  loading: boolean;
  error: string | null;
  selectedItem: MediaFile | null;
  uploadProgress: number;
  
  // Actions
  loadMedia: (folder: string) => Promise<void>;
  addItem: (item: MediaFile) => void;
  removeItem: (url: string) => Promise<boolean>;
  selectItem: (item: MediaFile | null) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  setUploadProgress: (progress: number) => void;
  
  // Upload actions
  uploadFile: (file: File, folder: string, cropConfig?: CropConfig) => Promise<MediaFile | null>;
  cropAndUpload: (imageUrl: string, cropArea: any, folder: string, config: CropConfig) => Promise<MediaFile | null>;
}

// Helper function to generate content hash
const generateContentHash = async (file: File): Promise<string> => {
  const arrayBuffer = await file.arrayBuffer();
  const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('').substring(0, 16);
};

// Helper function to deduplicate items
const deduplicateItems = (items: MediaFile[]): MediaFile[] => {
  const seen = new Set<string>();
  const unique = items.filter(item => {
    if (seen.has(item.url)) {
      console.warn(`🚫 STORE: Duplicate prevented for ${item.url}`);
      return false;
    }
    seen.add(item.url);
    return true;
  });
  
  if (items.length !== unique.length) {
    console.log(`📊 STORE: Deduplication ${items.length} → ${unique.length} items`);
  }
  
  return unique;
};

export const useMediaStore = create<MediaState>()(
  devtools(
    (set, get) => ({
      // Initial state
      items: [],
      loading: false,
      error: null,
      selectedItem: null,
      uploadProgress: 0,

      // Load media from API
      loadMedia: async (folder: string) => {
        console.log(`🔄 STORE: Loading media from folder: ${folder}`);
        set({ loading: true, error: null });

        try {
          const response = await fetch(`/api/media/list?folder=${folder}`);
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();
          const mediaList = data.data || [];
          
          console.log(`📥 STORE: API returned ${mediaList.length} items`);
          
          // Deduplicate at store level
          const uniqueItems = deduplicateItems(mediaList);
          
          set({ 
            items: uniqueItems, 
            loading: false,
            error: null 
          });
          
          console.log(`✅ STORE: Loaded ${uniqueItems.length} unique items`);
        } catch (error) {
          console.error('❌ STORE: Load error:', error);
          set({ 
            loading: false, 
            error: error instanceof Error ? error.message : 'Failed to load media' 
          });
        }
      },

      // Add item to store
      addItem: (item: MediaFile) => {
        const { items } = get();
        
        // Check for duplicates
        const exists = items.some(existing => existing.url === item.url);
        if (exists) {
          console.warn(`🚫 STORE: Duplicate add prevented for ${item.url}`);
          return;
        }
        
        console.log(`✅ STORE: Adding item ${item.originalName}`);
        set({ items: [item, ...items] });
      },

      // Remove item from store and server
      removeItem: async (url: string): Promise<boolean> => {
        console.log(`🗑️ STORE: Removing item ${url}`);
        
        try {
          const response = await fetch(`/api/media/delete?url=${encodeURIComponent(url)}`, {
            method: 'DELETE'
          });
          
          if (!response.ok) {
            throw new Error(`Failed to delete: ${response.statusText}`);
          }
          
          // Remove from store
          const { items } = get();
          const updatedItems = items.filter(item => item.url !== url);
          set({ items: updatedItems });
          
          console.log(`✅ STORE: Item removed ${url}`);
          return true;
        } catch (error) {
          console.error('❌ STORE: Delete error:', error);
          set({ error: error instanceof Error ? error.message : 'Failed to delete item' });
          return false;
        }
      },

      // Select item
      selectItem: (item: MediaFile | null) => {
        console.log(`🎯 STORE: Selected item ${item?.originalName || 'none'}`);
        set({ selectedItem: item });
      },

      // Clear error
      clearError: () => {
        set({ error: null });
      },

      // Set loading state
      setLoading: (loading: boolean) => {
        set({ loading });
      },

      // Set upload progress
      setUploadProgress: (progress: number) => {
        set({ uploadProgress: progress });
      },

      // Upload file
      uploadFile: async (file: File, folder: string, cropConfig?: CropConfig): Promise<MediaFile | null> => {
        console.log(`📤 STORE: Uploading file ${file.name} to ${folder}`);
        set({ uploadProgress: 0, error: null });

        try {
          // Generate content hash for deduplication
          const contentHash = await generateContentHash(file);
          console.log(`🔐 STORE: Generated content hash ${contentHash}`);

          const formData = new FormData();
          formData.append('file', file);
          formData.append('folder', folder);
          formData.append('contentHash', contentHash);
          
          if (cropConfig) {
            formData.append('cropConfig', JSON.stringify(cropConfig));
          }

          const response = await fetch('/api/media/upload', {
            method: 'POST',
            body: formData
          });

          if (!response.ok) {
            throw new Error(`Upload failed: ${response.statusText}`);
          }

          const result = await response.json();
          const newItem: MediaFile = result.data;

          console.log(`✅ STORE: Upload successful ${newItem.url}`);
          
          // Add to store
          get().addItem(newItem);
          set({ uploadProgress: 100 });

          return newItem;
        } catch (error) {
          console.error('❌ STORE: Upload error:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Upload failed',
            uploadProgress: 0 
          });
          return null;
        }
      },

      // Crop and upload
      cropAndUpload: async (imageUrl: string, cropArea: any, folder: string, config: CropConfig): Promise<MediaFile | null> => {
        console.log(`✂️ STORE: Cropping and uploading ${imageUrl}`);
        set({ uploadProgress: 0, error: null });

        try {
          const response = await fetch('/api/media/crop', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              imageUrl,
              cropArea,
              folder,
              config
            })
          });

          if (!response.ok) {
            throw new Error(`Crop failed: ${response.statusText}`);
          }

          const result = await response.json();
          const newItem: MediaFile = result.data;

          console.log(`✅ STORE: Crop successful ${newItem.url}`);
          
          // Add to store
          get().addItem(newItem);
          set({ uploadProgress: 100 });

          return newItem;
        } catch (error) {
          console.error('❌ STORE: Crop error:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Crop failed',
            uploadProgress: 0 
          });
          return null;
        }
      }
    }),
    {
      name: 'media-store',
      partialize: (state) => ({ 
        items: state.items,
        selectedItem: state.selectedItem 
      })
    }
  )
);
