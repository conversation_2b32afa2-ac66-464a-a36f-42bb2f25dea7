"use client"

import React, { useState } from 'react';
import { RobustMediaSelector } from './RobustMediaSelector';
import { MediaFile } from '@/stores/media-store';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertTriangle, Zap } from 'lucide-react';

export default function ComprehensiveTest() {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<MediaFile | undefined>(undefined);
  const [testResults, setTestResults] = useState<Array<{
    id: string;
    name: string;
    status: 'pending' | 'running' | 'passed' | 'failed';
    message: string;
    timestamp: string;
  }>>([]);

  const addTestResult = (name: string, status: 'passed' | 'failed', message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [...prev, {
      id: Date.now().toString(),
      name,
      status,
      message,
      timestamp
    }]);
  };

  const handleSelect = (media: MediaFile | undefined) => {
    setSelectedMedia(media);
    addTestResult(
      'Media Selection',
      'passed',
      `Selected: ${media ? media.originalName : 'None'}`
    );
  };

  const handleClose = () => {
    setIsOpen(false);
    addTestResult('Modal Close', 'passed', 'Modal closed successfully');
  };

  const handleOpen = () => {
    setIsOpen(true);
    addTestResult('Modal Open', 'passed', 'Modal opened successfully');
  };

  const runDuplicationTest = async () => {
    addTestResult('Duplication Test', 'running', 'Starting comprehensive duplication test...');
    
    // Test API directly
    try {
      const response = await fetch('/api/media/list?folder=projeler');
      const data = await response.json();
      
      if (data.success) {
        const items = data.data || [];
        const urls = items.map((item: any) => item.url);
        const uniqueUrls = new Set(urls);
        
        if (urls.length === uniqueUrls.size) {
          addTestResult('API Duplication Check', 'passed', `API returned ${items.length} unique items`);
        } else {
          addTestResult('API Duplication Check', 'failed', `API returned duplicates: ${urls.length} total, ${uniqueUrls.size} unique`);
        }
      } else {
        addTestResult('API Duplication Check', 'failed', `API error: ${data.error}`);
      }
    } catch (error) {
      addTestResult('API Duplication Check', 'failed', `Network error: ${error}`);
    }
  };

  const runUploadTest = () => {
    addTestResult('Upload Test', 'running', 'Open modal and test upload functionality');
    handleOpen();
  };

  const runCropTest = () => {
    addTestResult('Crop Test', 'running', 'Open modal and test cropping functionality');
    handleOpen();
  };

  const runDeleteTest = () => {
    addTestResult('Delete Test', 'running', 'Open modal and test delete confirmation');
    handleOpen();
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'passed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'running':
        return <div className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      passed: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800',
      running: 'bg-blue-100 text-blue-800',
      pending: 'bg-yellow-100 text-yellow-800'
    };
    return variants[status as keyof typeof variants] || variants.pending;
  };

  const passedTests = testResults.filter(t => t.status === 'passed').length;
  const failedTests = testResults.filter(t => t.status === 'failed').length;
  const totalTests = testResults.length;

  return (
    <div className="p-8 max-w-7xl mx-auto">
      <h1 className="text-3xl font-bold mb-8 flex items-center gap-3">
        <Zap className="h-8 w-8 text-blue-600" />
        Robust Media Selector - Comprehensive Test Suite
      </h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Test Controls */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-green-800">✅ New Architecture</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="text-sm space-y-2">
                <div>🏪 <strong>Zustand Store:</strong> Global state management</div>
                <div>🔄 <strong>No useEffect:</strong> Action-based updates only</div>
                <div>🛡️ <strong>Server Deduplication:</strong> API-level protection</div>
                <div>✂️ <strong>Advanced Cropping:</strong> Dimension controls</div>
                <div>🗑️ <strong>Delete Confirmation:</strong> Prevent accidents</div>
                <div>📤 <strong>Drag & Drop:</strong> Modern upload UX</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Test Controls</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button onClick={handleOpen} className="w-full">
                Open Media Selector
              </Button>
              
              <Button onClick={runDuplicationTest} variant="outline" className="w-full">
                Test API Duplication
              </Button>
              
              <Button onClick={runUploadTest} variant="outline" className="w-full">
                Test Upload Flow
              </Button>
              
              <Button onClick={runCropTest} variant="outline" className="w-full">
                Test Cropping
              </Button>
              
              <Button onClick={runDeleteTest} variant="outline" className="w-full">
                Test Delete Confirmation
              </Button>
              
              <Button onClick={clearResults} variant="ghost" className="w-full">
                Clear Results
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Test Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span>Total Tests:</span>
                  <Badge variant="outline">{totalTests}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Passed:</span>
                  <Badge className="bg-green-100 text-green-800">{passedTests}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Failed:</span>
                  <Badge className="bg-red-100 text-red-800">{failedTests}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Success Rate:</span>
                  <Badge variant="outline">
                    {totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0}%
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Selected Media */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Selected Media</CardTitle>
            </CardHeader>
            <CardContent>
              {selectedMedia ? (
                <div className="space-y-4">
                  <img 
                    src={selectedMedia.url} 
                    alt={selectedMedia.originalName} 
                    className="w-full h-48 object-cover rounded border"
                  />
                  <div className="space-y-2">
                    <div className="font-medium">{selectedMedia.originalName}</div>
                    <div className="text-sm text-gray-500">
                      Size: {(selectedMedia.size / 1024).toFixed(2)} KB
                    </div>
                    <div className="text-xs text-blue-600 break-all">
                      {selectedMedia.url}
                    </div>
                    {selectedMedia.contentHash && (
                      <div className="text-xs text-gray-500 font-mono">
                        Hash: {selectedMedia.contentHash}
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No media selected
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Critical Test Cases</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="p-3 bg-blue-50 rounded">
                  <strong>1. Upload Test:</strong> Upload image → verify no duplicates
                </div>
                <div className="p-3 bg-purple-50 rounded">
                  <strong>2. Crop Test:</strong> Select image → crop with dimensions → verify output
                </div>
                <div className="p-3 bg-red-50 rounded">
                  <strong>3. Delete Test:</strong> Delete image → confirm dialog → verify removal
                </div>
                <div className="p-3 bg-green-50 rounded">
                  <strong>4. Modal Test:</strong> Open/close multiple times → verify consistency
                </div>
                <div className="p-3 bg-yellow-50 rounded">
                  <strong>5. API Test:</strong> Check server response for duplicates
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Test Results */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {testResults.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    No test results yet. Run some tests to see results here.
                  </div>
                ) : (
                  testResults.map((result) => (
                    <div
                      key={result.id}
                      className="flex items-start gap-3 p-3 border rounded-lg"
                    >
                      {getStatusIcon(result.status)}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium text-sm">{result.name}</span>
                          <Badge className={getStatusBadge(result.status)}>
                            {result.status}
                          </Badge>
                        </div>
                        <div className="text-xs text-gray-600">{result.message}</div>
                        <div className="text-xs text-gray-400 mt-1">{result.timestamp}</div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Debug Console</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm space-y-2">
                <p>Open browser console (F12) to see detailed logging:</p>
                <ul className="list-disc list-inside space-y-1 text-xs text-gray-600">
                  <li>🏪 STORE: Zustand state updates</li>
                  <li>🔄 COMPONENT: Component actions</li>
                  <li>📡 API: Server responses</li>
                  <li>🚫 Duplicate prevention alerts</li>
                  <li>✅ Success confirmations</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <RobustMediaSelector
        open={isOpen}
        onClose={handleClose}
        onSelect={handleSelect}
        selectedMedia={selectedMedia}
        title="Robust Media Selector - Zero Duplicates"
        description="Complete rewrite with Zustand, advanced cropping, and delete confirmation"
        folder="projeler"
        acceptedTypes={["image/jpeg", "image/png", "image/webp"]}
        maxSizeMB={5}
        cropConfig={{
          targetWidth: 800,
          targetHeight: 600,
          quality: 90
        }}
      />
    </div>
  );
}
