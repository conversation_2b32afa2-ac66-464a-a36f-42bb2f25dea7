import { NextRequest, NextResponse } from "next/server";
import { unlink, stat } from "fs/promises";
import { join } from "path";

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const url = searchParams.get("url");
    const confirm = searchParams.get("confirm") === "true";
    
    console.log(`🗑️ API: Delete request for ${url} (confirmed: ${confirm})`);
    
    if (!url) {
      return NextResponse.json({ 
        success: false,
        error: "URL parameter is required" 
      }, { status: 400 });
    }
    
    // Security check: ensure URL is within public folder
    if (!url.startsWith('/')) {
      return NextResponse.json({ 
        success: false,
        error: "Invalid URL format" 
      }, { status: 400 });
    }
    
    // Convert URL to file path
    const filePath = join(process.cwd(), "public", url.replace(/^\//, ""));
    
    // Security check: ensure file is within public directory
    const publicDir = join(process.cwd(), "public");
    if (!filePath.startsWith(publicDir)) {
      return NextResponse.json({ 
        success: false,
        error: "Access denied: file outside public directory" 
      }, { status: 403 });
    }
    
    try {
      // Check if file exists
      const fileStat = await stat(filePath);
      
      if (!confirm) {
        // Return file info for confirmation
        return NextResponse.json({
          success: true,
          requiresConfirmation: true,
          fileInfo: {
            url,
            size: fileStat.size,
            createdAt: fileStat.birthtime.toISOString(),
            modifiedAt: fileStat.mtime.toISOString()
          },
          message: "File deletion requires confirmation"
        });
      }
      
      // Delete the file
      await unlink(filePath);
      
      console.log(`✅ API: File deleted successfully: ${url}`);
      
      return NextResponse.json({
        success: true,
        message: "File deleted successfully",
        deletedFile: {
          url,
          deletedAt: new Date().toISOString()
        }
      });
      
    } catch (err: any) {
      if (err.code === "ENOENT") {
        console.log(`⚠️ API: File not found: ${url}`);
        return NextResponse.json({ 
          success: false,
          error: "File not found" 
        }, { status: 404 });
      }
      
      console.error("❌ API: Delete error:", err);
      return NextResponse.json({ 
        success: false,
        error: "Failed to delete file",
        details: err.message
      }, { status: 500 });
    }
    
  } catch (error) {
    console.error("❌ API: Delete request error:", error);
    return NextResponse.json({ 
      success: false,
      error: "Invalid delete request",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
