"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Upload,
  Search,
  Image,
  FolderOpen,
  Plus,
  RefreshCw,
  Filter,
  FileX,
} from 'lucide-react';

import { useMediaSelector } from '../../MediaSelectorProvider';
import { gridItemVariants, fadeVariants } from '../../utils/animations';

interface EmptyStatesProps {
  type: 'empty' | 'no-results' | 'error';
  query?: string;
  error?: string;
}

export function EmptyStates({ type, query, error }: EmptyStatesProps) {
  const { state, actions } = useMediaSelector();
  const { config } = state;
  const labels = config.labels!;

  // Empty gallery state
  if (type === 'empty') {
    return (
      <motion.div
        variants={fadeVariants}
        initial="hidden"
        animate="visible"
        className="flex items-center justify-center min-h-[400px] p-8"
      >
        <Card className="max-w-md w-full bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950 border-dashed border-2">
          <CardContent className="p-8 text-center">
            <motion.div
              variants={gridItemVariants}
              className="space-y-6"
            >
              {/* Animated icon */}
              <motion.div
                animate={{
                  y: [0, -10, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
                className="mx-auto w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center"
              >
                <FolderOpen className="h-10 w-10 text-white" />
              </motion.div>

              {/* Content */}
              <div className="space-y-3">
                <h3 className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  {labels.emptyGallery}
                </h3>
                <p className="text-muted-foreground">
                  Get started by uploading your first files. You can drag and drop files or click the upload button.
                </p>
              </div>

              {/* Actions */}
              <div className="space-y-3">
                {config.enableUpload && (
                  <Button
                    onClick={() => actions.setTab?.('upload')}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Files
                  </Button>
                )}
                
                <Button
                  variant="outline"
                  onClick={actions.loadMedia}
                  className="w-full"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>

              {/* Tips */}
              <div className="text-xs text-muted-foreground space-y-1">
                <p>💡 Tip: You can upload multiple files at once</p>
                <p>📁 Supported: {config.acceptedTypes.join(', ')}</p>
              </div>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  // No search results state
  if (type === 'no-results') {
    return (
      <motion.div
        variants={fadeVariants}
        initial="hidden"
        animate="visible"
        className="flex items-center justify-center min-h-[300px] p-8"
      >
        <Card className="max-w-md w-full">
          <CardContent className="p-8 text-center">
            <motion.div
              variants={gridItemVariants}
              className="space-y-6"
            >
              {/* Animated search icon */}
              <motion.div
                animate={{
                  scale: [1, 1.1, 1],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
                className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center"
              >
                <Search className="h-8 w-8 text-gray-400" />
              </motion.div>

              {/* Content */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold">
                  {labels.noResults}
                </h3>
                {query && (
                  <p className="text-muted-foreground">
                    No files found for "<span className="font-medium">{query}</span>"
                  </p>
                )}
                <p className="text-sm text-muted-foreground">
                  Try adjusting your search terms or filters to find what you're looking for.
                </p>
              </div>

              {/* Actions */}
              <div className="space-y-2">
                <Button
                  variant="outline"
                  onClick={() => actions.setSearch('')}
                  className="w-full"
                >
                  <Search className="h-4 w-4 mr-2" />
                  Clear Search
                </Button>
                
                <Button
                  variant="ghost"
                  onClick={() => actions.setFilters({ type: 'all' })}
                  className="w-full text-sm"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Reset Filters
                </Button>
              </div>

              {/* Suggestions */}
              <div className="text-xs text-muted-foreground space-y-1">
                <p>💡 Try searching by:</p>
                <p>• File name or extension</p>
                <p>• Alt text or caption</p>
                <p>• Tags or metadata</p>
              </div>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  // Error state
  if (type === 'error') {
    return (
      <motion.div
        variants={fadeVariants}
        initial="hidden"
        animate="visible"
        className="flex items-center justify-center min-h-[300px] p-8"
      >
        <Card className="max-w-md w-full border-red-200 dark:border-red-800">
          <CardContent className="p-8 text-center">
            <motion.div
              variants={gridItemVariants}
              className="space-y-6"
            >
              {/* Error icon */}
              <motion.div
                animate={{
                  rotate: [0, 5, -5, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
                className="mx-auto w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center"
              >
                <FileX className="h-8 w-8 text-red-600 dark:text-red-400" />
              </motion.div>

              {/* Content */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-red-800 dark:text-red-200">
                  Something went wrong
                </h3>
                <p className="text-red-600 dark:text-red-400">
                  {error || 'Failed to load media files. Please try again.'}
                </p>
              </div>

              {/* Actions */}
              <div className="space-y-2">
                <Button
                  onClick={actions.loadMedia}
                  className="w-full"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => window.location.reload()}
                  className="w-full"
                >
                  Reload Page
                </Button>
              </div>

              {/* Help text */}
              <div className="text-xs text-muted-foreground">
                <p>If the problem persists, please contact support.</p>
              </div>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  return null;
}

// Specialized empty states
export function UploadEmptyState() {
  const { actions } = useMediaSelector();

  return (
    <motion.div
      variants={fadeVariants}
      initial="hidden"
      animate="visible"
      className="text-center py-12"
    >
      <motion.div
        animate={{
          y: [0, -5, 0],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut",
        }}
        className="mx-auto w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center mb-6"
      >
        <Plus className="h-8 w-8 text-white" />
      </motion.div>
      
      <h3 className="text-lg font-semibold mb-2">Ready to upload</h3>
      <p className="text-muted-foreground mb-6">
        Drag and drop files here or click browse to get started
      </p>
      
      <Button
        onClick={() => actions.setTab?.('gallery')}
        variant="outline"
      >
        <Image className="h-4 w-4 mr-2" />
        View Gallery
      </Button>
    </motion.div>
  );
}

export function FilterEmptyState({ activeFilters }: { activeFilters: string[] }) {
  const { actions } = useMediaSelector();

  return (
    <motion.div
      variants={fadeVariants}
      initial="hidden"
      animate="visible"
      className="text-center py-12"
    >
      <Filter className="mx-auto h-12 w-12 text-gray-400 mb-4" />
      <h3 className="text-lg font-semibold mb-2">No files match your filters</h3>
      <p className="text-muted-foreground mb-4">
        Try adjusting or removing some filters to see more results
      </p>
      
      <div className="space-y-2">
        <p className="text-sm text-muted-foreground">Active filters:</p>
        <div className="flex flex-wrap gap-1 justify-center">
          {activeFilters.map((filter) => (
            <span
              key={filter}
              className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs"
            >
              {filter}
            </span>
          ))}
        </div>
      </div>
      
      <Button
        onClick={() => actions.setFilters({ type: 'all' })}
        variant="outline"
        className="mt-4"
      >
        Clear All Filters
      </Button>
    </motion.div>
  );
}
