"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { CalendarIcon } from "lucide-react"
import { format } from "date-fns"
import { tr } from "date-fns/locale"
import { toast } from "@/hooks/use-toast"
import { MediaSelector, useProjectImageSelector, type MediaFile } from "@/components/media-selector";
import { getProjectImageUrl } from "@/config/project-media-config"
import { generateSlug } from "@/lib/utils"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { cn } from "@/lib/utils"

const projectSchema = z.object({
  ad: z.string().min(1, "Proje adı gereklidir").max(200, "Proje adı çok uzun"),
  slug: z.string().min(1, "Slug gereklidir").max(200, "Slug çok uzun"),
  aciklama: z.string().optional(),
  adres: z.string().optional(),
  baslangic_tarihi: z.date({
    required_error: "Başlangıç tarihi gereklidir",
  }),
  bitis_tarihi: z.date().optional(),
  project_image_url: z.string().optional(),
})

type ProjectFormData = z.infer<typeof projectSchema>

interface Project {
  id: string
  ad: string
  slug?: string
  aciklama?: string
  adres?: string
  baslangic_tarihi: string
  bitis_tarihi?: string
  project_image_url?: string
}

interface ProjectDialogProps {
  open: boolean
  onClose: () => void
  project?: Project | null
}

export function ProjectDialog({ open, onClose, project }: ProjectDialogProps) {
  const [loading, setLoading] = useState(false)
  const [isSlugAutoGenerated, setIsSlugAutoGenerated] = useState(false)
  const isEditing = !!project
  const [mediaSelectorOpen, setMediaSelectorOpen] = useState(false);

  const form = useForm<ProjectFormData>({
    resolver: zodResolver(projectSchema),
    defaultValues: {
      ad: "",
      slug: "",
      aciklama: "",
      adres: "",
      baslangic_tarihi: new Date(),
      bitis_tarihi: undefined,
      project_image_url: "",
    },
  })

  // Project image state for RobustMediaSelector
  const [selectedMedia, setSelectedMedia] = useState<MediaFile | undefined>(undefined);

  // Modal açılırken selectedMedia'yı sıfırla
  useEffect(() => {
    if (mediaSelectorOpen) {
      setSelectedMedia(undefined);
    }
  }, [mediaSelectorOpen]);

  // Proje adı değiştiğinde otomatik slug oluştur
  const handleAdChange = (value: string) => {
    form.setValue('ad', value)
    
    // Eğer slug manuel olarak değiştirilmemişse otomatik oluştur
    if (isSlugAutoGenerated || !isEditing) {
      const generatedSlug = generateSlug(value)
      form.setValue('slug', generatedSlug)
      setIsSlugAutoGenerated(true)
    }
  }

  // Slug manuel değiştirildiğinde flag'i güncelle
  const handleSlugChange = (value: string) => {
    form.setValue('slug', value)
    setIsSlugAutoGenerated(false)
  }

  // useEffect: Sadece proje değiştiğinde formu resetle, selectedMedia güncellemesi form resetlemesin
  useEffect(() => {
    if (project) {
      form.reset({
        ad: project.ad,
        slug: project.slug || generateSlug(project.ad),
        aciklama: project.aciklama || "",
        adres: project.adres || "",
        baslangic_tarihi: new Date(project.baslangic_tarihi),
        bitis_tarihi: project.bitis_tarihi ? new Date(project.bitis_tarihi) : undefined,
        project_image_url: project.project_image_url || "",
      });
      if (project.project_image_url) {
        setSelectedMedia({ url: project.project_image_url, originalName: "", size: 0 });
      } else {
        setSelectedMedia(undefined);
      }
      setIsSlugAutoGenerated(false);
    } else {
      form.reset({
        ad: "",
        slug: "",
        aciklama: "",
        adres: "",
        baslangic_tarihi: new Date(),
        bitis_tarihi: undefined,
        project_image_url: "",
      });
      setSelectedMedia(undefined);
      setIsSlugAutoGenerated(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [project]); // Sadece project değişince resetle

  // Tab veya görsel değişimlerinde toast tetiklenmesini engellemek için, toast'lar sadece handleSubmit içinde gösterilecek.

  // Form submitini sadece kullanıcı submit butonuna bastığında çalıştır
  const handleSubmit = async (data: ProjectFormData, event?: React.BaseSyntheticEvent) => {
    // Sadece submit butonundan geliyorsa çalıştır
    if (!(event && (event.nativeEvent as any)?.submitter)) {
      return;
    }
  try {
    setLoading(true)

    // project_image_url alanını selectedMedia'dan güncelle
    const payload = {
      ...data,
      project_image_url: selectedMedia?.url || ""
    };

    const url = isEditing ? `/api/projects/${project.slug}` : "/api/projects";
    const method = isEditing ? "PUT" : "POST"

    const response = await fetch(url, {
      method,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    })
    
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || "İşlem başarısız")
    }

    // Akıllı toast mesajı: sadece görsel değiştiyse farklı mesaj göster
    if (isEditing && project) {
      const oldProject = project;
      const onlyImageChanged =
        oldProject.ad === data.ad &&
        oldProject.slug === data.slug &&
        (oldProject.aciklama || "") === (data.aciklama || "") &&
        (oldProject.adres || "") === (data.adres || "") &&
        new Date(oldProject.baslangic_tarihi).toISOString() === data.baslangic_tarihi.toISOString() &&
        ((oldProject.bitis_tarihi ? new Date(oldProject.bitis_tarihi).toISOString() : "") === (data.bitis_tarihi ? data.bitis_tarihi.toISOString() : "")) &&
        (oldProject.project_image_url !== (selectedMedia?.url || ""));
      toast.success(
        onlyImageChanged ? "Proje görseli güncellendi!" : "Proje başarıyla güncellendi!",
        {
          title: "Başarılı",
          duration: 4000
        }
      )
    } else {
      toast.success(
        isEditing ? "Proje başarıyla güncellendi!" : "Yeni proje başarıyla oluşturuldu!",
        {
          title: "Başarılı",
          duration: 4000
        }
      )
    }

    // Kısa bir gecikme ekleyerek database'in güncellenmesini bekle
    setTimeout(() => {
      onClose()
    }, 100)
  } catch (error) {
    console.error("Error saving project:", error)
    
    // Hata bildirimi  
    toast.error(
      error instanceof Error ? error.message : "Beklenmeyen bir hata oluştu",
      {
        title: "Hata",
        duration: 6000
      }
    )
  } finally {
    setLoading(false)
  }
}

  return (
    <div className="w-full pl-0 md:pl-8">
      <Form {...form}>
        <form onSubmit={form.handleSubmit((data, event) => handleSubmit(data, event))} className="flex flex-col gap-8">
          {/* Sağ üstte kapatma butonu */}
          <div className="flex justify-end">
            <button
              type="button"
              className="text-gray-400 hover:text-gray-700 text-2xl font-bold focus:outline-none"
              aria-label="Kapat"
              onClick={onClose}
            >
              ×
            </button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <FormField
                control={form.control}
                name="ad"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Proje Adı *</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Proje adını girin" 
                        {...field} 
                        onChange={(e) => handleAdChange(e.target.value)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="slug"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Slug *</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="proje-adi-slug" 
                        {...field} 
                        onChange={(e) => handleSlugChange(e.target.value)}
                      />
                    </FormControl>
                    <FormMessage />
                    {isSlugAutoGenerated && (
                      <p className="text-xs text-muted-foreground">
                        Proje adından otomatik oluşturuldu
                      </p>
                    )}
                  </FormItem>
                )}
              />
            </div>

            {/* Project Image Uploader */}
            <FormField
              control={form.control}
              name="project_image_url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Proje Görseli</FormLabel>
                  <div className="flex flex-col gap-2">
                    <button
                      type="button"
                      className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 w-fit"
                      onClick={() => {
                        setSelectedMedia(undefined);
                        setMediaSelectorOpen(true);
                      }}
                    >
                      Proje Görseli Seç
                    </button>
                    {/* selectedMedia gösterimi sadece modal kapalıyken */}
                    {!mediaSelectorOpen && selectedMedia && (
                      <div className="flex items-center gap-4 mt-2 p-3 bg-gray-50 border rounded-lg shadow-sm">
                        <img src={selectedMedia.url} alt={selectedMedia.alt || selectedMedia.originalName} className="w-24 h-24 object-cover rounded border" />
                        <div className="flex flex-col gap-1">
                          <div className="font-medium text-sm text-gray-800 truncate max-w-[160px]">{selectedMedia.originalName || selectedMedia.url.split('/').pop()}</div>
                          <div className="text-xs text-gray-500">{selectedMedia.size ? `${(selectedMedia.size / 1024).toFixed(2)} KB` : ''}</div>
                          {selectedMedia.alt && <div className="text-xs text-gray-500 italic">{selectedMedia.alt}</div>}
                          <button
                            className="mt-2 px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 text-xs font-semibold transition"
                            onClick={() => setSelectedMedia(undefined)}
                            type="button"
                          >
                            Kaldır
                          </button>
                        </div>
                      </div>
                    )}
                    <MediaSelector
                      open={mediaSelectorOpen}
                      onClose={() => setMediaSelectorOpen(false)}
                      config={{
                        folder: 'projeler',
                        acceptedTypes: ['image/jpeg', 'image/png', 'image/webp'],
                        maxFileSize: 5 * 1024 * 1024, // 5MB
                        maxFiles: 1,
                        layout: 'dialog',
                        theme: 'auto',
                        showMetadata: true,
                        enableBulkActions: false,
                        enableSearch: true,
                        enableUpload: true,
                        cropConfig: {
                          targetWidth: 800,
                          targetHeight: 600,
                          quality: 90,
                        },
                        labels: {
                          title: 'Proje Görseli Seç',
                          description: 'Projeye ait görseli seçin veya yükleyin',
                          selectButton: 'Seç',
                          cancelButton: 'İptal',
                        },
                        onSelect: (files) => {
                          const media = files[0] || null;
                          setSelectedMedia(media);
                          form.setValue('project_image_url', media?.url || '');
                        },
                      }}
                    />
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="aciklama"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Açıklama</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Proje açıklaması (opsiyonel)"
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="adres"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Adres</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Proje adresi (opsiyonel)"
                      className="min-h-[60px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="baslangic_tarihi"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Başlangıç Tarihi *</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP", { locale: tr })
                            ) : (
                              <span>Tarih seçin</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date("1900-01-01")
                          }
                          initialFocus
                          locale={tr}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="bitis_tarihi"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Bitiş Tarihi</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP", { locale: tr })
                            ) : (
                              <span>Tarih seçin (opsiyonel)</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < form.getValues("baslangic_tarihi")
                          }
                          initialFocus
                          locale={tr}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex flex-col md:flex-row justify-end gap-3 pt-2">
              <Button type="button" variant="outline" onClick={onClose}>
                İptal
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? "Kaydediliyor..." : isEditing ? "Güncelle" : "Kaydet"}
              </Button>
            </div>
        </form>
      </Form>
    </div>
  )
}
