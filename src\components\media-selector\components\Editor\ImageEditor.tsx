"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  X,
  Save,
  Edit,
  Tag,
  Image as ImageIcon,
  Crop,
  Palette,
  Info,
} from 'lucide-react';

import type { MediaFile } from '../../types';
import { modalVariants, tabVariants } from '../../utils/animations';

interface ImageEditorProps {
  item: MediaFile;
  onSave: (updates: Partial<MediaFile>) => void;
  onCancel: () => void;
}

export function ImageEditor({ item, onSave, onCancel }: ImageEditorProps) {
  const [formData, setFormData] = useState({
    alt: item.alt || '',
    caption: item.caption || '',
    tags: item.tags || [],
  });
  const [newTag, setNewTag] = useState('');
  const [activeTab, setActiveTab] = useState('metadata');

  // Handle form changes
  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle tag operations
  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()],
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove),
    }));
  };

  // Handle save
  const handleSave = () => {
    onSave({
      alt: formData.alt,
      caption: formData.caption,
      tags: formData.tags,
    });
  };

  // Handle key press for tag input
  const handleTagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  const isImage = item.mimeType.startsWith('image/');

  return (
    <Dialog open onOpenChange={onCancel}>
      <DialogContent className="max-w-4xl h-[80vh] p-0 overflow-hidden">
        <motion.div
          variants={modalVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="h-full flex flex-col"
        >
          {/* Header */}
          <DialogHeader className="px-6 py-4 border-b bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950">
            <div className="flex items-center justify-between">
              <div>
                <DialogTitle className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Edit Media
                </DialogTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  {item.originalName}
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onCancel}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </DialogHeader>

          {/* Content */}
          <div className="flex-1 flex overflow-hidden">
            {/* Preview */}
            <div className="flex-1 bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-6">
              {isImage ? (
                <div className="max-w-full max-h-full">
                  <img
                    src={item.url}
                    alt={item.alt || item.originalName}
                    className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
                  />
                </div>
              ) : (
                <div className="text-center">
                  <div className="w-32 h-32 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-lg flex items-center justify-center mb-4">
                    <ImageIcon className="h-16 w-16 text-gray-400" />
                  </div>
                  <p className="text-muted-foreground">
                    {item.mimeType.split('/')[1].toUpperCase()} File
                  </p>
                </div>
              )}
            </div>

            {/* Editor Panel */}
            <div className="w-96 border-l bg-white dark:bg-gray-950 flex flex-col">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
                <TabsList className="grid w-full grid-cols-3 m-4 mb-0">
                  <TabsTrigger value="metadata" className="text-xs">
                    <Info className="h-3 w-3 mr-1" />
                    Info
                  </TabsTrigger>
                  <TabsTrigger value="tags" className="text-xs">
                    <Tag className="h-3 w-3 mr-1" />
                    Tags
                  </TabsTrigger>
                  <TabsTrigger value="edit" className="text-xs" disabled={!isImage}>
                    <Edit className="h-3 w-3 mr-1" />
                    Edit
                  </TabsTrigger>
                </TabsList>

                <div className="flex-1 overflow-auto">
                  <motion.div
                    key={activeTab}
                    variants={tabVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                  >
                    <TabsContent value="metadata" className="p-4 space-y-4 m-0">
                      {/* Alt Text */}
                      <div className="space-y-2">
                        <Label htmlFor="alt">Alt Text</Label>
                        <Textarea
                          id="alt"
                          placeholder="Describe this image for accessibility..."
                          value={formData.alt}
                          onChange={(e) => handleChange('alt', e.target.value)}
                          className="min-h-[80px]"
                        />
                        <p className="text-xs text-muted-foreground">
                          Help screen readers understand this image
                        </p>
                      </div>

                      {/* Caption */}
                      <div className="space-y-2">
                        <Label htmlFor="caption">Caption</Label>
                        <Textarea
                          id="caption"
                          placeholder="Add a caption or description..."
                          value={formData.caption}
                          onChange={(e) => handleChange('caption', e.target.value)}
                          className="min-h-[80px]"
                        />
                        <p className="text-xs text-muted-foreground">
                          Public description shown with the image
                        </p>
                      </div>

                      {/* File Info */}
                      <div className="space-y-2 pt-4 border-t">
                        <h4 className="font-medium text-sm">File Information</h4>
                        <div className="space-y-1 text-xs text-muted-foreground">
                          <div className="flex justify-between">
                            <span>Size:</span>
                            <span>{(item.size / 1024 / 1024).toFixed(2)} MB</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Type:</span>
                            <span>{item.mimeType}</span>
                          </div>
                          {item.dimensions && (
                            <div className="flex justify-between">
                              <span>Dimensions:</span>
                              <span>{item.dimensions.width} × {item.dimensions.height}</span>
                            </div>
                          )}
                          <div className="flex justify-between">
                            <span>Created:</span>
                            <span>{new Date(item.createdAt).toLocaleDateString()}</span>
                          </div>
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="tags" className="p-4 space-y-4 m-0">
                      {/* Add Tag */}
                      <div className="space-y-2">
                        <Label htmlFor="newTag">Add Tags</Label>
                        <div className="flex gap-2">
                          <Input
                            id="newTag"
                            placeholder="Enter tag..."
                            value={newTag}
                            onChange={(e) => setNewTag(e.target.value)}
                            onKeyPress={handleTagKeyPress}
                            className="flex-1"
                          />
                          <Button
                            onClick={addTag}
                            disabled={!newTag.trim()}
                            size="sm"
                          >
                            Add
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Press Enter or click Add to create a tag
                        </p>
                      </div>

                      {/* Current Tags */}
                      <div className="space-y-2">
                        <Label>Current Tags</Label>
                        {formData.tags.length > 0 ? (
                          <div className="flex flex-wrap gap-2">
                            {formData.tags.map((tag) => (
                              <Badge
                                key={tag}
                                variant="secondary"
                                className="cursor-pointer hover:bg-red-100 hover:text-red-700"
                                onClick={() => removeTag(tag)}
                              >
                                {tag}
                                <X className="h-3 w-3 ml-1" />
                              </Badge>
                            ))}
                          </div>
                        ) : (
                          <p className="text-sm text-muted-foreground">
                            No tags added yet
                          </p>
                        )}
                      </div>

                      {/* Tag Suggestions */}
                      <div className="space-y-2 pt-4 border-t">
                        <Label>Suggested Tags</Label>
                        <div className="flex flex-wrap gap-2">
                          {['photo', 'image', 'media', 'content'].map((suggestion) => (
                            <Badge
                              key={suggestion}
                              variant="outline"
                              className="cursor-pointer hover:bg-blue-50 hover:border-blue-300"
                              onClick={() => {
                                if (!formData.tags.includes(suggestion)) {
                                  setFormData(prev => ({
                                    ...prev,
                                    tags: [...prev.tags, suggestion],
                                  }));
                                }
                              }}
                            >
                              + {suggestion}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="edit" className="p-4 space-y-4 m-0">
                      <div className="text-center text-muted-foreground">
                        <Crop className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                        <p>Image editing tools</p>
                        <p className="text-xs">Coming soon...</p>
                      </div>
                    </TabsContent>
                  </motion.div>
                </div>
              </Tabs>

              {/* Footer Actions */}
              <div className="p-4 border-t bg-muted/30 flex gap-2">
                <Button variant="outline" onClick={onCancel} className="flex-1">
                  Cancel
                </Button>
                <Button onClick={handleSave} className="flex-1">
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
