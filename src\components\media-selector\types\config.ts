// Configuration presets and utilities

import type { MediaSelectorConfig, MediaSelectorLabels } from './index';

// Default configuration
export const DEFAULT_CONFIG: MediaSelectorConfig = {
  folder: 'media',
  apiEndpoints: {
    upload: '/api/upload',
    list: '/api/media',
    delete: '/api/media/delete',
  },
  acceptedTypes: ['image/*'],
  maxFileSize: 10 * 1024 * 1024, // 10MB
  maxFiles: 10,
  layout: 'dialog',
  theme: 'auto',
  showMetadata: true,
  enableBulkActions: true,
  enableSearch: true,
  enableUpload: true,
  cropConfig: {
    quality: 90,
    minZoom: 1,
    maxZoom: 3,
  },
  labels: DEFAULT_LABELS,
};

// Default labels (English)
export const DEFAULT_LABELS: MediaSelectorLabels = {
  // General
  title: 'Media Selector',
  description: 'Select or upload media files',
  selectButton: 'Select',
  cancelButton: 'Cancel',
  
  // Upload
  uploadTitle: 'Upload Files',
  uploadDescription: 'Drag and drop files here or click to browse',
  dragDropText: 'Drop files here to upload',
  browseFiles: 'Browse Files',
  uploadProgress: 'Uploading...',
  
  // Gallery
  galleryTitle: 'Media Gallery',
  searchPlaceholder: 'Search files...',
  noResults: 'No files found',
  emptyGallery: 'No files uploaded yet',
  
  // Actions
  select: 'Select',
  delete: 'Delete',
  edit: 'Edit',
  crop: 'Crop',
  download: 'Download',
  
  // Bulk actions
  selectAll: 'Select All',
  deselectAll: 'Deselect All',
  deleteSelected: 'Delete Selected',
  selectedCount: '{count} selected',
  
  // Metadata
  altText: 'Alt Text',
  caption: 'Caption',
  tags: 'Tags',
  
  // Errors
  fileTooLarge: 'File is too large',
  invalidFileType: 'Invalid file type',
  uploadFailed: 'Upload failed',
  deleteFailed: 'Delete failed',
  networkError: 'Network error',
};

// Turkish labels
export const TURKISH_LABELS: MediaSelectorLabels = {
  // General
  title: 'Medya Seçici',
  description: 'Medya dosyalarını seçin veya yükleyin',
  selectButton: 'Seç',
  cancelButton: 'İptal',
  
  // Upload
  uploadTitle: 'Dosya Yükle',
  uploadDescription: 'Dosyaları buraya sürükleyip bırakın veya göz atmak için tıklayın',
  dragDropText: 'Yüklemek için dosyaları buraya bırakın',
  browseFiles: 'Dosyalara Göz At',
  uploadProgress: 'Yükleniyor...',
  
  // Gallery
  galleryTitle: 'Medya Galerisi',
  searchPlaceholder: 'Dosya ara...',
  noResults: 'Dosya bulunamadı',
  emptyGallery: 'Henüz dosya yüklenmemiş',
  
  // Actions
  select: 'Seç',
  delete: 'Sil',
  edit: 'Düzenle',
  crop: 'Kırp',
  download: 'İndir',
  
  // Bulk actions
  selectAll: 'Tümünü Seç',
  deselectAll: 'Seçimi Kaldır',
  deleteSelected: 'Seçilenleri Sil',
  selectedCount: '{count} seçili',
  
  // Metadata
  altText: 'Alt Metin',
  caption: 'Açıklama',
  tags: 'Etiketler',
  
  // Errors
  fileTooLarge: 'Dosya çok büyük',
  invalidFileType: 'Geçersiz dosya türü',
  uploadFailed: 'Yükleme başarısız',
  deleteFailed: 'Silme başarısız',
  networkError: 'Ağ hatası',
};

// Configuration presets for common use cases
export const PROJECT_IMAGES_CONFIG: Partial<MediaSelectorConfig> = {
  folder: 'projeler',
  acceptedTypes: ['image/jpeg', 'image/png', 'image/webp'],
  maxFileSize: 5 * 1024 * 1024, // 5MB
  cropConfig: {
    targetWidth: 800,
    targetHeight: 600,
    quality: 90,
  },
  labels: {
    title: 'Proje Görseli Seç',
    description: 'Projeye ait görseli seçin veya yükleyin',
  },
};

export const BLOG_IMAGES_CONFIG: Partial<MediaSelectorConfig> = {
  folder: 'blog',
  acceptedTypes: ['image/*'],
  maxFileSize: 2 * 1024 * 1024, // 2MB
  cropConfig: {
    targetWidth: 1200,
    targetHeight: 630,
    aspectRatio: 1200 / 630,
    quality: 85,
  },
};

export const PRODUCT_IMAGES_CONFIG: Partial<MediaSelectorConfig> = {
  folder: 'products',
  acceptedTypes: ['image/jpeg', 'image/png', 'image/webp'],
  maxFileSize: 3 * 1024 * 1024, // 3MB
  maxFiles: 5,
  cropConfig: {
    aspectRatio: 1, // Square
    targetWidth: 800,
    targetHeight: 800,
    quality: 90,
  },
};

export const DOCUMENT_CONFIG: Partial<MediaSelectorConfig> = {
  folder: 'documents',
  acceptedTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  maxFileSize: 10 * 1024 * 1024, // 10MB
  showMetadata: false,
  enableBulkActions: false,
};

// Utility function to merge configurations
export function mergeConfig(base: MediaSelectorConfig, override: Partial<MediaSelectorConfig>): MediaSelectorConfig {
  return {
    ...base,
    ...override,
    apiEndpoints: {
      ...base.apiEndpoints,
      ...override.apiEndpoints,
    },
    cropConfig: {
      ...base.cropConfig,
      ...override.cropConfig,
    },
    labels: {
      ...base.labels,
      ...override.labels,
    },
  };
}

// Validation function for configuration
export function validateConfig(config: Partial<MediaSelectorConfig>): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!config.folder) {
    errors.push('Folder is required');
  }
  
  if (!config.acceptedTypes || config.acceptedTypes.length === 0) {
    errors.push('At least one accepted file type is required');
  }
  
  if (!config.maxFileSize || config.maxFileSize <= 0) {
    errors.push('Max file size must be greater than 0');
  }
  
  if (config.maxFiles && config.maxFiles <= 0) {
    errors.push('Max files must be greater than 0');
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
}
