"use client"

import { useQuery } from "@tanstack/react-query"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  ArrowUpIcon,
  ArrowDownIcon,
  Wrench,
  Clock,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Activity,
  Users,
  Building,
  Calendar,
  Plus,
  ExternalLink,
  Timer,
  AlertCircleIcon
} from "lucide-react"
import { useRouter } from "next/navigation"
import { formatDistanceToNow } from "date-fns"
import { tr } from "date-fns/locale"

const fetcher = async () => {
  const res = await fetch("/api/faults/stats")
  return res.json()
}

const appointmentsFetcher = async () => {
  const res = await fetch("/api/appointments")
  return res.json()
}

const projectsFetcher = async () => {
  const res = await fetch("/api/projects")
  return res.json()
}

const usersFetcher = async () => {
  const res = await fetch("/api/users")
  return res.json()
}

function DashboardOverview() {
  const router = useRouter()

  const { data: faultStats, isLoading: faultsLoading } = useQuery({
    queryKey: ["faults", "stats"],
    queryFn: fetcher
  })

  const { data: appointmentsData, isLoading: appointmentsLoading } = useQuery({
    queryKey: ["appointments"],
    queryFn: appointmentsFetcher
  })

  const { data: projectsData, isLoading: projectsLoading } = useQuery({
    queryKey: ["projects"],
    queryFn: projectsFetcher
  })

  const { data: usersData, isLoading: usersLoading } = useQuery({
    queryKey: ["users"],
    queryFn: usersFetcher
  })

  if (faultsLoading || appointmentsLoading || projectsLoading || usersLoading || !faultStats) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-40 bg-white/50 rounded-xl animate-pulse"></div>
          ))}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-white/50 rounded-xl animate-pulse"></div>
          ))}
        </div>
      </div>
    )
  }

  // Process appointments data
  const appointments = appointmentsData || []
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000)

  // Filter appointments
  const upcomingAppointments = appointments.filter((apt: any) => {
    const aptDate = new Date(apt.randevu_tarihi)
    return aptDate >= now && apt.durum !== 'TAMAMLANDI' && apt.durum !== 'IPTAL'
  }).slice(0, 3)

  const overdueAppointments = appointments.filter((apt: any) => {
    const aptDate = new Date(apt.randevu_tarihi)
    return aptDate < now && apt.durum !== 'TAMAMLANDI' && apt.durum !== 'IPTAL'
  }).slice(0, 3)

  const todayAppointments = appointments.filter((apt: any) => {
    const aptDate = new Date(apt.randevu_tarihi)
    return aptDate >= today && aptDate < tomorrow
  })

  // Priority stats for main focus
  const priorityStats = [
    {
      title: "Açık Arızalar",
      value: (faultStats.beklemede || 0) + (faultStats.devamEdiyor || 0),
      change: `${(faultStats.yuzdelik || 0) >= 0 ? '+' : ''}${(faultStats.yuzdelik || 0).toFixed(1)}%`,
      trend: (faultStats.yuzdelik || 0) >= 0 ? "up" : "down",
      icon: AlertTriangle,
      gradient: "from-red-500 to-orange-500",
      bgGradient: "from-red-50 to-orange-50",
      description: "Çözüm bekleyen kritik arızalar",
      urgent: true,
      action: () => router.push('/arizalar?status=acik')
    },
    {
      title: "Bugünkü Randevular",
      value: todayAppointments.length,
      change: `${upcomingAppointments.length} yaklaşan`,
      trend: "neutral",
      icon: Calendar,
      gradient: "from-blue-500 to-cyan-500",
      bgGradient: "from-blue-50 to-cyan-50",
      description: "Bugün planlanmış randevular",
      action: () => router.push('/randevu')
    },
    {
      title: "Geciken Randevular",
      value: overdueAppointments.length,
      change: "Acil müdahale",
      trend: overdueAppointments.length > 0 ? "up" : "down",
      icon: Timer,
      gradient: "from-red-600 to-pink-600",
      bgGradient: "from-red-50 to-pink-50",
      description: "Zamanı geçmiş randevular",
      urgent: overdueAppointments.length > 0,
      action: () => router.push('/randevu?filter=overdue')
    }
  ]

  // Secondary stats for lower priority information
  const secondaryStats = [
    {
      title: "Toplam Arıza",
      value: faultStats.toplam,
      change: `${(faultStats.yuzdelik || 0) >= 0 ? '+' : ''}${(faultStats.yuzdelik || 0).toFixed(1)}%`,
      trend: (faultStats.yuzdelik || 0) >= 0 ? "up" : "down",
      icon: Wrench,
      gradient: "from-blue-500 to-cyan-500",
      bgGradient: "from-blue-50 to-cyan-50",
      description: "Sistemdeki toplam arıza"
    },
    {
      title: "Tamamlandı",
      value: faultStats.tamamlandi,
      change: "+18%",
      trend: "up",
      icon: CheckCircle,
      gradient: "from-green-500 to-emerald-500",
      bgGradient: "from-green-50 to-emerald-50",
      description: "Başarıyla çözülen"
    },
    {
      title: "Aktif Projeler",
      value: projectsData?.projects?.length || 0,
      change: "+2",
      trend: "up",
      icon: Building,
      gradient: "from-indigo-500 to-purple-500",
      bgGradient: "from-indigo-50 to-purple-50",
      description: "Yönetilen proje sayısı"
    },
    {
      title: "Toplam Kullanıcı",
      value: usersData?.users?.length || 0,
      change: "+5",
      trend: "up",
      icon: Users,
      gradient: "from-teal-500 to-cyan-500",
      bgGradient: "from-teal-50 to-cyan-50",
      description: "Sistem kullanıcıları"
    }
  ]

  return (
    <div className="space-y-8">
      {/* PRIORITY SECTION - Critical Information */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            <AlertCircleIcon className="h-5 w-5 text-red-500" />
            Acil Dikkat Gereken
          </h2>
          <div className="flex gap-2">
            <Button
              size="sm"
              onClick={() => router.push('/arizalar/yeni')}
              className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700"
            >
              <Plus className="h-4 w-4 mr-1" />
              Arıza Bildir
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => router.push('/randevu')}
            >
              <Calendar className="h-4 w-4 mr-1" />
              Randevu Yönet
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {priorityStats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <Card
                key={stat.title}
                className={`relative overflow-hidden border-0 shadow-lg bg-gradient-to-br ${stat.bgGradient} hover:shadow-xl transition-all duration-300 cursor-pointer ${
                  stat.urgent ? 'ring-2 ring-red-200 shadow-red-100' : ''
                }`}
                onClick={stat.action}
              >
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-700">
                    {stat.title}
                    {stat.urgent && (
                      <Badge className="ml-2 bg-red-100 text-red-700 text-xs">
                        Acil
                      </Badge>
                    )}
                  </CardTitle>
                  <div className={`p-2 rounded-lg bg-gradient-to-r ${stat.gradient}`}>
                    <Icon className="h-5 w-5 text-white" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className={`text-3xl font-bold ${stat.urgent ? 'text-red-700' : 'text-gray-900'}`}>
                        {stat.value || 0}
                      </div>
                      <p className="text-xs text-gray-600 mt-1">
                        {stat.description}
                      </p>
                    </div>
                    <div className="flex flex-col items-end">
                      <div className="flex items-center space-x-1">
                        {stat.trend === "up" ? (
                          <ArrowUpIcon className="h-4 w-4 text-red-500" />
                        ) : stat.trend === "down" ? (
                          <ArrowDownIcon className="h-4 w-4 text-green-500" />
                        ) : null}
                      </div>
                      <Badge
                        variant="secondary"
                        className="text-xs mt-1"
                      >
                        {stat.change}
                      </Badge>
                      <ExternalLink className="h-3 w-3 text-gray-400 mt-1" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>

      {/* UPCOMING APPOINTMENTS SECTION */}
      {upcomingAppointments.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
            <Calendar className="h-5 w-5 text-blue-500" />
            Yaklaşan Randevular
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {upcomingAppointments.map((appointment: any) => (
              <Card
                key={appointment.id}
                className="hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => router.push(`/randevu/${appointment.id}`)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <p className="font-medium text-sm text-gray-900">
                        {appointment.ariza?.baslik || 'Randevu'}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        {appointment.ariza?.daire?.blok?.proje?.ad} - {appointment.ariza?.daire?.blok?.ad} / {appointment.ariza?.daire?.numara}
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        <Clock className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-600">
                          {formatDistanceToNow(new Date(appointment.randevu_tarihi), {
                            addSuffix: true,
                            locale: tr
                          })}
                        </span>
                      </div>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {new Date(appointment.randevu_tarihi).toLocaleDateString('tr-TR', {
                        day: '2-digit',
                        month: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* OVERDUE APPOINTMENTS SECTION */}
      {overdueAppointments.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-red-700 flex items-center gap-2">
            <Timer className="h-5 w-5 text-red-500" />
            Geciken Randevular
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {overdueAppointments.map((appointment: any) => (
              <Card
                key={appointment.id}
                className="border-red-200 bg-red-50 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => router.push(`/randevu/${appointment.id}`)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <p className="font-medium text-sm text-red-900">
                        {appointment.ariza?.baslik || 'Randevu'}
                      </p>
                      <p className="text-xs text-red-600 mt-1">
                        {appointment.ariza?.daire?.blok?.proje?.ad} - {appointment.ariza?.daire?.blok?.ad} / {appointment.ariza?.daire?.numara}
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        <AlertTriangle className="h-3 w-3 text-red-500" />
                        <span className="text-xs text-red-700 font-medium">
                          {formatDistanceToNow(new Date(appointment.randevu_tarihi), {
                            addSuffix: true,
                            locale: tr
                          })}
                        </span>
                      </div>
                    </div>
                    <Badge className="bg-red-100 text-red-700 text-xs">
                      Gecikti
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* SECONDARY STATS SECTION */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-700">Genel İstatistikler</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {secondaryStats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <Card
                key={stat.title}
                className={`relative overflow-hidden border-0 shadow-md bg-gradient-to-br ${stat.bgGradient} hover:shadow-lg transition-all duration-300`}
              >
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-700">
                    {stat.title}
                  </CardTitle>
                  <div className={`p-2 rounded-lg bg-gradient-to-r ${stat.gradient}`}>
                    <Icon className="h-4 w-4 text-white" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-2xl font-bold text-gray-900">
                        {stat.value || 0}
                      </div>
                      <p className="text-xs text-gray-600 mt-1">
                        {stat.description}
                      </p>
                    </div>
                    <div className="flex items-center space-x-1">
                      {stat.trend === "up" ? (
                        <ArrowUpIcon className="h-4 w-4 text-green-500" />
                      ) : (
                        <ArrowDownIcon className="h-4 w-4 text-red-500" />
                      )}
                      <Badge
                        variant="secondary"
                        className={`text-xs ${
                          stat.trend === "up" ? "text-green-700 bg-green-100" : "text-red-700 bg-red-100"
                        }`}
                      >
                        {stat.change}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>
    </div>
  )
}

export default DashboardOverview 