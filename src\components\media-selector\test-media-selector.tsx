"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { MediaSelector } from './MediaSelector';
import type { MediaFile } from './types';

export function TestMediaSelector() {
  const [open, setOpen] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<MediaFile | null>(null);

  return (
    <div className="p-8 space-y-4">
      <h1 className="text-2xl font-bold">Media Selector Test</h1>
      
      <div className="space-y-4">
        <Button onClick={() => setOpen(true)}>
          Open Media Selector
        </Button>
        
        {selectedMedia && (
          <div className="p-4 border rounded-lg">
            <h3 className="font-semibold mb-2">Selected Media:</h3>
            <div className="space-y-2">
              <p><strong>Name:</strong> {selectedMedia.originalName}</p>
              <p><strong>URL:</strong> {selectedMedia.url}</p>
              <p><strong>Size:</strong> {Math.round((selectedMedia.size || 0) / 1024)} KB</p>
              <p><strong>Type:</strong> {selectedMedia.mimeType}</p>
              {selectedMedia.isCropped && (
                <p className="text-green-600"><strong>Status:</strong> Cropped Image</p>
              )}
              {selectedMedia.url && (
                <img 
                  src={selectedMedia.url} 
                  alt={selectedMedia.alt || selectedMedia.originalName}
                  className="max-w-xs max-h-48 object-contain border rounded"
                />
              )}
            </div>
          </div>
        )}
      </div>

      <MediaSelector
        open={open}
        onClose={() => setOpen(false)}
        config={{
          folder: 'test',
          acceptedTypes: ['image/jpeg', 'image/png', 'image/webp'],
          maxFileSize: 5 * 1024 * 1024, // 5MB
          maxFiles: 1,
          layout: 'dialog',
          theme: 'auto',
          showMetadata: true,
          enableBulkActions: false,
          enableSearch: true,
          enableUpload: true,
          cropConfig: {
            targetWidth: 800,
            targetHeight: 600,
            quality: 90,
          },
          labels: {
            title: 'Select Media File',
            description: 'Choose an image or upload a new one',
            selectButton: 'Select',
            cancelButton: 'Cancel',
          },
          onSelect: (files) => {
            const media = files[0] || null;
            setSelectedMedia(media);
            setOpen(false);
          },
        }}
      />
    </div>
  );
}
