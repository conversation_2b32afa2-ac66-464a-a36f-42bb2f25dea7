import type { CropData, CropConfig } from '../types';

// Create canvas for image processing
function createCanvas(width: number, height: number): HTMLCanvasElement {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  return canvas;
}

// Load image from URL or File
export function loadImage(source: string | File): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      if (typeof source !== 'string') {
        URL.revokeObjectURL(img.src);
      }
      resolve(img);
    };
    
    img.onerror = () => {
      if (typeof source !== 'string') {
        URL.revokeObjectURL(img.src);
      }
      reject(new Error('Failed to load image'));
    };
    
    if (typeof source === 'string') {
      img.src = source;
    } else {
      img.src = URL.createObjectURL(source);
    }
  });
}

// Get image dimensions
export async function getImageDimensions(source: string | File): Promise<{ width: number; height: number }> {
  const img = await loadImage(source);
  return { width: img.naturalWidth, height: img.naturalHeight };
}

// Crop image using canvas
export async function cropImage(
  source: string | File,
  cropData: CropData,
  config?: CropConfig
): Promise<Blob> {
  const img = await loadImage(source);
  
  const canvas = createCanvas(
    config?.targetWidth || cropData.width,
    config?.targetHeight || cropData.height
  );
  
  const ctx = canvas.getContext('2d');
  if (!ctx) throw new Error('Could not get canvas context');
  
  // Apply rotation if specified
  if (cropData.rotation) {
    ctx.translate(canvas.width / 2, canvas.height / 2);
    ctx.rotate((cropData.rotation * Math.PI) / 180);
    ctx.translate(-canvas.width / 2, -canvas.height / 2);
  }
  
  // Draw cropped image
  ctx.drawImage(
    img,
    cropData.x,
    cropData.y,
    cropData.width,
    cropData.height,
    0,
    0,
    canvas.width,
    canvas.height
  );
  
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error('Failed to create blob from canvas'));
        }
      },
      'image/jpeg',
      (config?.quality || 90) / 100
    );
  });
}

// Resize image
export async function resizeImage(
  source: string | File,
  targetWidth: number,
  targetHeight: number,
  quality: number = 90
): Promise<Blob> {
  const img = await loadImage(source);
  const canvas = createCanvas(targetWidth, targetHeight);
  const ctx = canvas.getContext('2d');
  
  if (!ctx) throw new Error('Could not get canvas context');
  
  ctx.drawImage(img, 0, 0, targetWidth, targetHeight);
  
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error('Failed to create blob from canvas'));
        }
      },
      'image/jpeg',
      quality / 100
    );
  });
}

// Compress image
export async function compressImage(
  source: string | File,
  quality: number = 80,
  maxWidth?: number,
  maxHeight?: number
): Promise<Blob> {
  const img = await loadImage(source);
  
  let { width, height } = img;
  
  // Calculate new dimensions if max dimensions are specified
  if (maxWidth || maxHeight) {
    const aspectRatio = width / height;
    
    if (maxWidth && width > maxWidth) {
      width = maxWidth;
      height = width / aspectRatio;
    }
    
    if (maxHeight && height > maxHeight) {
      height = maxHeight;
      width = height * aspectRatio;
    }
  }
  
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');
  
  if (!ctx) throw new Error('Could not get canvas context');
  
  ctx.drawImage(img, 0, 0, width, height);
  
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error('Failed to create blob from canvas'));
        }
      },
      'image/jpeg',
      quality / 100
    );
  });
}

// Generate thumbnail
export async function generateThumbnail(
  source: string | File,
  size: number = 200,
  quality: number = 80
): Promise<Blob> {
  const img = await loadImage(source);
  const canvas = createCanvas(size, size);
  const ctx = canvas.getContext('2d');
  
  if (!ctx) throw new Error('Could not get canvas context');
  
  // Calculate crop area for square thumbnail
  const minDimension = Math.min(img.width, img.height);
  const x = (img.width - minDimension) / 2;
  const y = (img.height - minDimension) / 2;
  
  ctx.drawImage(
    img,
    x, y, minDimension, minDimension,
    0, 0, size, size
  );
  
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error('Failed to create blob from canvas'));
        }
      },
      'image/jpeg',
      quality / 100
    );
  });
}

// Apply image filters
export async function applyFilter(
  source: string | File,
  filter: 'grayscale' | 'sepia' | 'blur' | 'brightness' | 'contrast',
  intensity: number = 1
): Promise<Blob> {
  const img = await loadImage(source);
  const canvas = createCanvas(img.width, img.height);
  const ctx = canvas.getContext('2d');
  
  if (!ctx) throw new Error('Could not get canvas context');
  
  // Apply CSS filter
  switch (filter) {
    case 'grayscale':
      ctx.filter = `grayscale(${intensity})`;
      break;
    case 'sepia':
      ctx.filter = `sepia(${intensity})`;
      break;
    case 'blur':
      ctx.filter = `blur(${intensity}px)`;
      break;
    case 'brightness':
      ctx.filter = `brightness(${intensity})`;
      break;
    case 'contrast':
      ctx.filter = `contrast(${intensity})`;
      break;
  }
  
  ctx.drawImage(img, 0, 0);
  
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error('Failed to create blob from canvas'));
        }
      },
      'image/jpeg',
      90 / 100
    );
  });
}

// Convert image format
export async function convertImageFormat(
  source: string | File,
  format: 'jpeg' | 'png' | 'webp',
  quality: number = 90
): Promise<Blob> {
  const img = await loadImage(source);
  const canvas = createCanvas(img.width, img.height);
  const ctx = canvas.getContext('2d');
  
  if (!ctx) throw new Error('Could not get canvas context');
  
  // For PNG, fill with white background if converting from transparent format
  if (format === 'jpeg') {
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  }
  
  ctx.drawImage(img, 0, 0);
  
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error('Failed to create blob from canvas'));
        }
      },
      `image/${format}`,
      format === 'png' ? undefined : quality / 100
    );
  });
}

// Calculate optimal crop area for aspect ratio
export function calculateCropArea(
  imageWidth: number,
  imageHeight: number,
  aspectRatio: number
): CropData {
  const imageAspectRatio = imageWidth / imageHeight;
  
  let cropWidth: number;
  let cropHeight: number;
  
  if (imageAspectRatio > aspectRatio) {
    // Image is wider than target aspect ratio
    cropHeight = imageHeight;
    cropWidth = cropHeight * aspectRatio;
  } else {
    // Image is taller than target aspect ratio
    cropWidth = imageWidth;
    cropHeight = cropWidth / aspectRatio;
  }
  
  const x = (imageWidth - cropWidth) / 2;
  const y = (imageHeight - cropHeight) / 2;
  
  return {
    x,
    y,
    width: cropWidth,
    height: cropHeight,
    zoom: 1,
    rotation: 0,
  };
}

// Get image EXIF data (basic implementation)
export async function getImageExif(file: File): Promise<Record<string, any> | null> {
  try {
    // This is a simplified implementation
    // In a real application, you might want to use a library like 'exif-js'
    const arrayBuffer = await file.arrayBuffer();
    const dataView = new DataView(arrayBuffer);
    
    // Check for JPEG EXIF marker
    if (dataView.getUint16(0) !== 0xFFD8) {
      return null; // Not a JPEG
    }
    
    // Basic EXIF parsing would go here
    // For now, return null as this requires a full EXIF parser
    return null;
  } catch (error) {
    console.error('Error reading EXIF data:', error);
    return null;
  }
}
