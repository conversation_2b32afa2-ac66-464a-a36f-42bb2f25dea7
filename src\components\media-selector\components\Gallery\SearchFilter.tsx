"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Slider } from '@/components/ui/slider';
import {
  Search,
  Filter,
  X,
  Calendar as CalendarIcon,
  HardDrive,
  Tag,
} from 'lucide-react';

import { useMediaSelector } from '../../MediaSelectorProvider';
import { formatFileSize } from '../../utils/fileValidation';
import { searchVariants } from '../../utils/animations';
import { cn } from '@/lib/utils';

export function SearchFilter() {
  const { state, actions } = useMediaSelector();
  const { searchQuery, filters, items } = state;
  const [localSearch, setLocalSearch] = useState(searchQuery);
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      actions.setSearch(localSearch);
    }, 300);

    return () => clearTimeout(timer);
  }, [localSearch, actions]);

  // Calculate filter stats
  const stats = React.useMemo(() => {
    const types = items.reduce((acc, item) => {
      const type = item.mimeType.split('/')[0];
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const sizes = items.map(item => item.size);
    const minSize = Math.min(...sizes);
    const maxSize = Math.max(...sizes);

    const allTags = items.flatMap(item => item.tags || []);
    const uniqueTags = [...new Set(allTags)];

    return { types, minSize, maxSize, uniqueTags };
  }, [items]);

  // Handle filter changes
  const handleTypeFilter = (type: string) => {
    actions.setFilters({
      ...filters,
      type: type === 'all' ? 'all' : type as any,
    });
  };

  const handleSizeFilter = (range: number[]) => {
    actions.setFilters({
      ...filters,
      sizeRange: {
        min: range[0],
        max: range[1],
      },
    });
  };

  const handleDateFilter = (dateRange: { start: Date; end: Date } | undefined) => {
    actions.setFilters({
      ...filters,
      dateRange,
    });
  };

  const handleTagFilter = (tag: string) => {
    const currentTags = filters.tags || [];
    const newTags = currentTags.includes(tag)
      ? currentTags.filter(t => t !== tag)
      : [...currentTags, tag];
    
    actions.setFilters({
      ...filters,
      tags: newTags.length > 0 ? newTags : undefined,
    });
  };

  const clearFilters = () => {
    actions.setFilters({ type: 'all' });
    setLocalSearch('');
  };

  const hasActiveFilters = 
    filters.type !== 'all' ||
    filters.dateRange ||
    filters.sizeRange ||
    (filters.tags && filters.tags.length > 0) ||
    searchQuery;

  return (
    <div className="space-y-3">
      {/* Search input */}
      <motion.div
        variants={searchVariants}
        whileFocus="focused"
        className="relative"
      >
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          placeholder="Search files..."
          value={localSearch}
          onChange={(e) => setLocalSearch(e.target.value)}
          className="pl-10 pr-10 bg-white/50 backdrop-blur-sm border-gray-200 focus:border-blue-500 focus:ring-blue-500/20"
        />
        {localSearch && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setLocalSearch('')}
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </motion.div>

      {/* Quick filters */}
      <div className="flex items-center gap-2 flex-wrap">
        <Button
          variant={showAdvanced ? "default" : "outline"}
          size="sm"
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="h-8"
        >
          <Filter className="h-3 w-3 mr-1" />
          Filters
        </Button>

        {/* Type filter */}
        <Select value={filters.type || 'all'} onValueChange={handleTypeFilter}>
          <SelectTrigger className="w-auto h-8 text-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types ({items.length})</SelectItem>
            {Object.entries(stats.types).map(([type, count]) => (
              <SelectItem key={type} value={type}>
                {type.charAt(0).toUpperCase() + type.slice(1)} ({count})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Active filter badges */}
        {filters.tags?.map((tag) => (
          <Badge
            key={tag}
            variant="secondary"
            className="h-8 cursor-pointer hover:bg-red-100 hover:text-red-700"
            onClick={() => handleTagFilter(tag)}
          >
            <Tag className="h-3 w-3 mr-1" />
            {tag}
            <X className="h-3 w-3 ml-1" />
          </Badge>
        ))}

        {filters.dateRange && (
          <Badge
            variant="secondary"
            className="h-8 cursor-pointer hover:bg-red-100 hover:text-red-700"
            onClick={() => handleDateFilter(undefined)}
          >
            <CalendarIcon className="h-3 w-3 mr-1" />
            Date Range
            <X className="h-3 w-3 ml-1" />
          </Badge>
        )}

        {filters.sizeRange && (
          <Badge
            variant="secondary"
            className="h-8 cursor-pointer hover:bg-red-100 hover:text-red-700"
            onClick={() => handleSizeFilter([stats.minSize, stats.maxSize])}
          >
            <HardDrive className="h-3 w-3 mr-1" />
            Size Range
            <X className="h-3 w-3 ml-1" />
          </Badge>
        )}

        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="h-8 text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            Clear All
          </Button>
        )}
      </div>

      {/* Advanced filters */}
      {showAdvanced && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 dark:bg-gray-900 rounded-lg border"
        >
          {/* Date range filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-2">
              <CalendarIcon className="h-4 w-4" />
              Date Range
            </label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  {filters.dateRange
                    ? `${filters.dateRange.start.toLocaleDateString()} - ${filters.dateRange.end.toLocaleDateString()}`
                    : "Select date range"
                  }
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="range"
                  selected={filters.dateRange ? {
                    from: filters.dateRange.start,
                    to: filters.dateRange.end,
                  } : undefined}
                  onSelect={(range) => {
                    if (range?.from && range?.to) {
                      handleDateFilter({ start: range.from, end: range.to });
                    }
                  }}
                  numberOfMonths={2}
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Size range filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-2">
              <HardDrive className="h-4 w-4" />
              File Size
            </label>
            <div className="px-2">
              <Slider
                value={filters.sizeRange ? [filters.sizeRange.min, filters.sizeRange.max] : [stats.minSize, stats.maxSize]}
                onValueChange={handleSizeFilter}
                min={stats.minSize}
                max={stats.maxSize}
                step={1024}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>{formatFileSize(filters.sizeRange?.min || stats.minSize)}</span>
                <span>{formatFileSize(filters.sizeRange?.max || stats.maxSize)}</span>
              </div>
            </div>
          </div>

          {/* Tags filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-2">
              <Tag className="h-4 w-4" />
              Tags
            </label>
            <div className="flex flex-wrap gap-1 max-h-20 overflow-y-auto">
              {stats.uniqueTags.map((tag) => (
                <Badge
                  key={tag}
                  variant={filters.tags?.includes(tag) ? "default" : "outline"}
                  className="cursor-pointer text-xs"
                  onClick={() => handleTagFilter(tag)}
                >
                  {tag}
                </Badge>
              ))}
              {stats.uniqueTags.length === 0 && (
                <span className="text-sm text-gray-500">No tags found</span>
              )}
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
}
