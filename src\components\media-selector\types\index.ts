// Core media file interface
export interface MediaFile {
  id: string;
  url: string;
  originalName: string;
  filename: string;
  mimeType: string;
  size: number;
  createdAt: string;
  updatedAt?: string;
  
  // Image-specific properties
  dimensions?: {
    width: number;
    height: number;
  };
  
  // Thumbnails
  thumbnailSmall?: string;
  thumbnailMedium?: string;
  thumbnailLarge?: string;
  
  // Metadata
  alt?: string;
  caption?: string;
  tags?: string[];
  
  // Processing info
  contentHash?: string;
  isDuplicate?: boolean;
}

// Configuration interfaces
export interface MediaSelectorConfig {
  // Storage settings
  folder: string;
  apiEndpoints: {
    upload: string;
    list: string;
    delete?: string;
  };
  
  // File restrictions
  acceptedTypes: string[];
  maxFileSize: number; // in bytes
  maxFiles?: number;
  
  // UI customization
  layout: 'dialog' | 'inline' | 'compact';
  theme: 'light' | 'dark' | 'auto';
  showMetadata: boolean;
  enableBulkActions: boolean;
  enableSearch: boolean;
  enableUpload: boolean;
  
  // Image processing
  cropConfig?: CropConfig;
  
  // Callbacks
  onSelect?: (files: MediaFile[]) => void;
  onUpload?: (file: MediaFile) => void;
  onDelete?: (fileId: string) => void;
  onError?: (error: string) => void;
  
  // Localization
  labels?: Partial<MediaSelectorLabels>;
}

export interface CropConfig {
  aspectRatio?: number;
  targetWidth?: number;
  targetHeight?: number;
  quality?: number;
  minZoom?: number;
  maxZoom?: number;
}

export interface MediaSelectorLabels {
  // General
  title: string;
  description: string;
  selectButton: string;
  cancelButton: string;
  
  // Upload
  uploadTitle: string;
  uploadDescription: string;
  dragDropText: string;
  browseFiles: string;
  uploadProgress: string;
  
  // Gallery
  galleryTitle: string;
  searchPlaceholder: string;
  noResults: string;
  emptyGallery: string;
  
  // Actions
  select: string;
  delete: string;
  edit: string;
  crop: string;
  download: string;
  
  // Bulk actions
  selectAll: string;
  deselectAll: string;
  deleteSelected: string;
  selectedCount: string;
  
  // Metadata
  altText: string;
  caption: string;
  tags: string;
  
  // Errors
  fileTooLarge: string;
  invalidFileType: string;
  uploadFailed: string;
  deleteFailed: string;
  networkError: string;
}

// State management types
export interface MediaSelectorState {
  // Media items
  items: MediaFile[];
  selectedItems: Set<string>;
  
  // UI state
  view: 'grid' | 'list';
  currentTab: 'gallery' | 'upload';
  
  // Loading states
  loading: boolean;
  uploading: boolean;
  uploadProgress: Record<string, number>;
  
  // Editor state
  editingItem: MediaFile | null;
  cropData: CropData | null;
  
  // Search and filter
  searchQuery: string;
  filters: MediaFilters;
  
  // Error handling
  errors: Record<string, string>;
  
  // Configuration
  config: MediaSelectorConfig;
}

export interface CropData {
  x: number;
  y: number;
  width: number;
  height: number;
  zoom: number;
  rotation: number;
}

export interface MediaFilters {
  type?: 'all' | 'image' | 'video' | 'document';
  dateRange?: {
    start: Date;
    end: Date;
  };
  sizeRange?: {
    min: number;
    max: number;
  };
  tags?: string[];
}

// Action types for reducer
export type MediaSelectorAction =
  | { type: 'SET_CONFIG'; payload: MediaSelectorConfig }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_UPLOADING'; payload: boolean }
  | { type: 'SET_ITEMS'; payload: MediaFile[] }
  | { type: 'ADD_ITEM'; payload: MediaFile }
  | { type: 'UPDATE_ITEM'; payload: { id: string; updates: Partial<MediaFile> } }
  | { type: 'DELETE_ITEM'; payload: string }
  | { type: 'SELECT_ITEM'; payload: string }
  | { type: 'DESELECT_ITEM'; payload: string }
  | { type: 'TOGGLE_SELECTION'; payload: string }
  | { type: 'SELECT_ALL' }
  | { type: 'CLEAR_SELECTION' }
  | { type: 'SET_VIEW'; payload: 'grid' | 'list' }
  | { type: 'SET_TAB'; payload: 'gallery' | 'upload' }
  | { type: 'SET_SEARCH'; payload: string }
  | { type: 'SET_FILTERS'; payload: MediaFilters }
  | { type: 'SET_UPLOAD_PROGRESS'; payload: { fileId: string; progress: number } }
  | { type: 'START_EDIT'; payload: MediaFile }
  | { type: 'SAVE_EDIT'; payload: { id: string; updates: Partial<MediaFile> } }
  | { type: 'CANCEL_EDIT' }
  | { type: 'SET_CROP_DATA'; payload: CropData | null }
  | { type: 'SET_ERROR'; payload: { key: string; message: string } }
  | { type: 'CLEAR_ERROR'; payload: string }
  | { type: 'CLEAR_ALL_ERRORS' };

// Hook return types
export interface UseMediaSelectorReturn {
  state: MediaSelectorState;
  actions: {
    loadMedia: () => Promise<void>;
    uploadFiles: (files: File[]) => Promise<void>;
    deleteItem: (id: string) => Promise<void>;
    selectItem: (id: string) => void;
    toggleSelection: (id: string) => void;
    selectAll: () => void;
    clearSelection: () => void;
    setSearch: (query: string) => void;
    setFilters: (filters: MediaFilters) => void;
    startEdit: (item: MediaFile) => void;
    saveEdit: (id: string, updates: Partial<MediaFile>) => Promise<void>;
    cancelEdit: () => void;
  };
}

// Component prop types
export interface MediaSelectorProps {
  config: MediaSelectorConfig;
  open?: boolean;
  onClose?: () => void;
  className?: string;
}

export interface MediaGridProps {
  items: MediaFile[];
  selectedItems: Set<string>;
  onSelect: (id: string) => void;
  onToggleSelection: (id: string) => void;
  onEdit: (item: MediaFile) => void;
  onDelete: (id: string) => void;
  view: 'grid' | 'list';
  loading?: boolean;
}

export interface DropZoneProps {
  onDrop: (files: File[]) => void;
  acceptedTypes: string[];
  maxFileSize: number;
  maxFiles?: number;
  disabled?: boolean;
  className?: string;
}

// Utility types
export type FileValidationResult = {
  valid: boolean;
  errors: string[];
};

export type UploadResult = {
  success: boolean;
  file?: MediaFile;
  error?: string;
};

// Re-export all types
export * from './media';
export * from './config';
export * from './api';
