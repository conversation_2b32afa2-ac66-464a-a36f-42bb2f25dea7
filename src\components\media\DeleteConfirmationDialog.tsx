"use client"

import React from 'react';
import { MediaFile } from '@/stores/media-store';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { AlertTriangle, FileImage } from 'lucide-react';

interface DeleteConfirmationDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  mediaItem: MediaFile | null;
}

export const DeleteConfirmationDialog: React.FC<DeleteConfirmationDialogProps> = ({
  open,
  onClose,
  onConfirm,
  mediaItem
}) => {
  if (!mediaItem) return null;

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'Unknown date';
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onClose}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            Delete Media File
          </AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                Are you sure you want to delete this file? This action cannot be undone.
              </p>
              
              {/* File preview */}
              <div className="border rounded-lg p-4 bg-gray-50">
                <div className="flex items-start gap-3">
                  {/* Image thumbnail */}
                  <div className="flex-shrink-0">
                    <img
                      src={mediaItem.url}
                      alt={mediaItem.originalName}
                      className="w-16 h-16 object-cover rounded border"
                      onError={(e) => {
                        // Fallback to file icon if image fails to load
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const parent = target.parentElement;
                        if (parent) {
                          parent.innerHTML = '<div class="w-16 h-16 bg-gray-200 rounded border flex items-center justify-center"><svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path></svg></div>';
                        }
                      }}
                    />
                  </div>
                  
                  {/* File details */}
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {mediaItem.originalName}
                    </h4>
                    <div className="mt-1 space-y-1">
                      <p className="text-xs text-gray-500">
                        Size: {formatFileSize(mediaItem.size)}
                      </p>
                      <p className="text-xs text-gray-500">
                        Created: {formatDate(mediaItem.createdAt)}
                      </p>
                      <p className="text-xs text-gray-500 truncate">
                        URL: {mediaItem.url}
                      </p>
                      {mediaItem.contentHash && (
                        <p className="text-xs text-gray-500 font-mono">
                          Hash: {mediaItem.contentHash}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Warning message */}
              <div className="bg-red-50 border border-red-200 rounded p-3">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                  <div className="text-sm">
                    <p className="font-medium text-red-800">Warning</p>
                    <p className="text-red-700 mt-1">
                      This will permanently delete the file from the server. 
                      Any references to this file in your content will be broken.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter>
          <AlertDialogCancel onClick={onClose}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
          >
            Delete File
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
