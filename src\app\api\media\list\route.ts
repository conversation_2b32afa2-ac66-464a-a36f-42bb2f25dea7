import { NextRequest, NextResponse } from "next/server";
import { readdir, stat } from "fs/promises";
import { join } from "path";
import crypto from "crypto";

interface MediaFile {
  url: string;
  originalName: string;
  size: number;
  id: string;
  contentHash?: string;
  createdAt: string;
  dimensions?: {
    width: number;
    height: number;
  };
}

// Helper function to generate file content hash
const generateFileHash = async (filePath: string): Promise<string> => {
  try {
    const fs = await import("fs/promises");
    const fileBuffer = await fs.readFile(filePath);
    return crypto.createHash('sha256').update(fileBuffer).digest('hex').substring(0, 16);
  } catch {
    return '';
  }
};

// Helper function to deduplicate files
const deduplicateFiles = (files: MediaFile[]): MediaFile[] => {
  const seenUrls = new Set<string>();
  const seenHashes = new Set<string>();
  
  const unique = files.filter(file => {
    // Check URL duplicates
    if (seenUrls.has(file.url)) {
      console.warn(`🚫 API: Duplicate URL prevented: ${file.url}`);
      return false;
    }
    
    // Check content hash duplicates (if available)
    if (file.contentHash && seenHashes.has(file.contentHash)) {
      console.warn(`🚫 API: Duplicate content prevented: ${file.originalName} (hash: ${file.contentHash})`);
      return false;
    }
    
    seenUrls.add(file.url);
    if (file.contentHash) {
      seenHashes.add(file.contentHash);
    }
    
    return true;
  });
  
  if (files.length !== unique.length) {
    console.log(`📊 API: Deduplication ${files.length} → ${unique.length} files`);
  }
  
  return unique;
};

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const folder = searchParams.get("folder") || "media";
    const folderPath = join(process.cwd(), "public", folder);
    
    console.log(`📁 API: Listing files in ${folderPath}`);
    
    let files: MediaFile[] = [];
    
    try {
      const fileNames = await readdir(folderPath);
      const filteredFileNames = fileNames.filter(name => 
        !name.endsWith('.db') && 
        !name.startsWith('.') &&
        /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(name)
      );
      
      console.log(`📁 API: Found ${filteredFileNames.length} image files`);
      
      files = await Promise.all(
        filteredFileNames.map(async (name) => {
          const filePath = join(folderPath, name);
          const fileStat = await stat(filePath);
          const contentHash = await generateFileHash(filePath);
          
          return {
            url: `/${folder}/${name}`,
            originalName: name,
            size: fileStat.size,
            id: name,
            contentHash,
            createdAt: fileStat.birthtime.toISOString(),
            // TODO: Add image dimensions detection
          };
        })
      );
      
      // Server-side deduplication
      const uniqueFiles = deduplicateFiles(files);
      
      console.log(`✅ API: Returning ${uniqueFiles.length} unique files`);
      
      return NextResponse.json({ 
        success: true,
        data: uniqueFiles,
        meta: {
          total: uniqueFiles.length,
          folder,
          deduplicationApplied: files.length !== uniqueFiles.length
        }
      });
      
    } catch (e) {
      console.log(`📁 API: Folder ${folderPath} not found or empty, returning empty list`);
      return NextResponse.json({ 
        success: true,
        data: [],
        meta: {
          total: 0,
          folder,
          deduplicationApplied: false
        }
      });
    }
    
  } catch (error) {
    console.error("❌ API: Media list error:", error);
    return NextResponse.json({ 
      success: false,
      error: "Failed to list media files",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
