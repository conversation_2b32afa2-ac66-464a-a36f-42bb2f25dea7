"use client";

import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { X, Grid, List, Upload, Image } from 'lucide-react';

import { MediaSelectorProvider, useMediaSelector } from './MediaSelectorProvider';
import { MediaGrid } from './components/Gallery/MediaGrid';
import { DropZone } from './components/Upload/DropZone';
import { SearchFilter } from './components/Gallery/SearchFilter';
import { BulkActions } from './components/Gallery/BulkActions';
import { ProgressTracker } from './components/Upload/ProgressTracker';
import { ImageEditor } from './components/Editor/ImageEditor';
import { CropEditor } from './components/Editor/CropEditor';

import type { MediaSelectorProps, MediaSelectorConfig } from './types';
import { modalVariants, backdropVariants, tabVariants } from './utils/animations';
import { cn } from '@/lib/utils';

// Main MediaSelector component content
function MediaSelectorContent({ onClose, className }: { onClose?: () => void; className?: string }) {
  const { state, actions } = useMediaSelector();

  // Early return if state is not ready
  if (!state) {
    return <div>Loading...</div>;
  }

  const { config, currentTab, loading, uploading, selectedItems, editingItem, errors } = state;
  // Safely extract editMode with multiple fallbacks
  const editMode = state.editMode ?? null;
  const labels = config.labels!;

  // Load media on mount
  useEffect(() => {
    if (currentTab === 'gallery') {
      actions.loadMedia();
    }
  }, [currentTab]); // Remove actions from dependency to prevent infinite loop

  // Handle file selection
  const handleSelect = () => {
    const selectedFiles = state.items.filter(item => selectedItems.has(item.id));
    config.onSelect?.(selectedFiles);
    onClose?.();
  };

  // Render based on layout
  if (config.layout === 'inline') {
    return (
      <div className={cn('w-full h-full', className)}>
        <MediaSelectorInner />
      </div>
    );
  }

  if (config.layout === 'compact') {
    return (
      <div className={cn('w-full max-w-md', className)}>
        <MediaSelectorInner compact />
      </div>
    );
  }

  // Dialog layout (default)
  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-6xl h-[80vh] p-0 overflow-hidden">
        <motion.div
          variants={modalVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="h-full flex flex-col"
        >
          <DialogHeader className="px-6 py-4 border-b bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950">
            <div className="flex items-center justify-between">
              <div>
                <DialogTitle className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  {labels.title}
                </DialogTitle>
                {labels.description && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {labels.description}
                  </p>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </DialogHeader>

          <div className="flex-1 overflow-hidden">
            <MediaSelectorInner />
          </div>

          {/* Footer with actions */}
          <div className="px-6 py-4 border-t bg-muted/30 flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              {selectedItems.size > 0 && (
                <span>
                  {labels.selectedCount.replace('{count}', selectedItems.size.toString())}
                </span>
              )}
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={onClose}>
                {labels.cancelButton}
              </Button>
              <Button
                onClick={handleSelect}
                disabled={selectedItems.size === 0}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                {labels.selectButton}
              </Button>
            </div>
          </div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}

// Inner component with tabs and content
function MediaSelectorInner({ compact = false }: { compact?: boolean }) {
  const { state, actions } = useMediaSelector();
  const { config, currentTab, selectedItems, uploading, editingItem } = state;
  const labels = config.labels!;

  return (
    <div className="h-full flex flex-col">
      {/* Tabs */}
      <Tabs
        value={currentTab}
        onValueChange={(value) => actions.setTab(value as 'gallery' | 'upload')}
        className="flex-1 flex flex-col"
      >
        <TabsList className={cn(
          "grid w-full grid-cols-2 mx-6 mt-4",
          compact && "mx-2 mt-2"
        )}>
          <TabsTrigger key="gallery" value="gallery" className="flex items-center gap-2">
            <Image className="h-4 w-4" />
            {labels.galleryTitle}
          </TabsTrigger>
          {config.enableUpload && (
            <TabsTrigger key="upload" value="upload" className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              {labels.uploadTitle}
            </TabsTrigger>
          )}
        </TabsList>

        <div className="flex-1 overflow-hidden">
          <TabsContent
            value="gallery"
            className="h-full m-0 p-0 data-[state=active]:flex data-[state=active]:flex-col"
          >
            <AnimatePresence mode="wait">
              <motion.div
                key="gallery"
                variants={tabVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="flex-1 flex flex-col overflow-hidden"
              >
                {/* Gallery header */}
                <div className={cn(
                  "px-6 py-4 border-b space-y-4",
                  compact && "px-2 py-2 space-y-2"
                )}>
                  {config.enableSearch && <SearchFilter />}

                  <div className="flex items-center justify-between">
                    {/* View toggle */}
                    <div className="flex items-center gap-1 bg-muted rounded-lg p-1">
                      <Button
                        variant={state.view === 'grid' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => actions.setView('grid')}
                        className="h-8 w-8 p-0"
                      >
                        <Grid className="h-4 w-4" />
                      </Button>
                      <Button
                        variant={state.view === 'list' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => actions.setView('list')}
                        className="h-8 w-8 p-0"
                      >
                        <List className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* Bulk actions */}
                    {config.enableBulkActions && selectedItems.size > 0 && (
                      <BulkActions />
                    )}
                  </div>
                </div>

                {/* Gallery content */}
                <div className="flex-1 overflow-auto">
                  <MediaGrid />
                </div>
              </motion.div>
            </AnimatePresence>
          </TabsContent>

          {config.enableUpload && (
            <TabsContent
              value="upload"
              className="h-full m-0 p-0 data-[state=active]:flex data-[state=active]:flex-col"
            >
              <AnimatePresence mode="wait">
                <motion.div
                  key="upload"
                  variants={tabVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                  className="flex-1 flex flex-col overflow-hidden"
                >
                  <div className={cn("p-6", compact && "p-2")}>
                    <DropZone />
                  </div>

                  {uploading && (
                    <div className={cn("px-6 pb-6", compact && "px-2 pb-2")}>
                      <ProgressTracker />
                    </div>
                  )}
                </motion.div>
              </AnimatePresence>
            </TabsContent>
          )}
        </div>
      </Tabs>

      {/* Editor Modals */}
      <AnimatePresence>
        {editingItem && editMode === 'edit' && (
          <ImageEditor
            item={editingItem}
            onSave={(updates) => actions.saveEdit(editingItem.id, updates)}
            onCancel={actions.cancelEdit}
          />
        )}
        {editingItem && editMode === 'crop' && (
          <CropEditor
            item={editingItem}
            onSave={(croppedFile) => {
              // Add the cropped file to the gallery and close editor
              actions.loadMedia(); // Refresh gallery to show new cropped image
              actions.cancelEdit();
              onClose?.(); // Close the media selector if desired
            }}
            onCancel={actions.cancelEdit}
          />
        )}
      </AnimatePresence>
    </div>
  );
}

// Main MediaSelector component with provider
export function MediaSelector({ config, open = true, onClose, className }: MediaSelectorProps) {
  return (
    <MediaSelectorProvider config={config}>
      <AnimatePresence>
        {open && (
          <MediaSelectorContent onClose={onClose} className={className} />
        )}
      </AnimatePresence>
    </MediaSelectorProvider>
  );
}

// Export hook for external use
export { useMediaSelector } from './MediaSelectorProvider';
