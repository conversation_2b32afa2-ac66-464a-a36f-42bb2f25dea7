// Test script to verify MediaSelector imports work correctly

import { MediaSelector, MediaSelectorProvider, useMediaSelector } from '@/components/media-selector';
import type { MediaFile, MediaSelectorConfig } from '@/components/media-selector';

// Test that all exports are available
console.log('MediaSelector:', typeof MediaSelector);
console.log('MediaSelectorProvider:', typeof MediaSelectorProvider);
console.log('useMediaSelector:', typeof useMediaSelector);

// Test configuration
const testConfig: MediaSelectorConfig = {
  folder: 'test',
  apiEndpoints: {
    upload: '/api/upload',
    list: '/api/media',
  },
  acceptedTypes: ['image/*'],
  maxFileSize: 5 * 1024 * 1024,
  layout: 'dialog',
  theme: 'auto',
  showMetadata: true,
  enableBulkActions: true,
  enableSearch: true,
  enableUpload: true,
};

console.log('Test config created successfully:', testConfig);

export default function testImports() {
  return 'All imports working correctly';
}
