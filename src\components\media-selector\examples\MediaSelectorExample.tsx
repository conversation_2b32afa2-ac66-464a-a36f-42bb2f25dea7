"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Image, Upload, FileText, Package } from 'lucide-react';

import { MediaSelector, useProjectImageSelector, useBlogImageSelector, useProductImageSelector, useDocumentSelector, type MediaFile } from '../index';

export function MediaSelectorExample() {
  const [selectedFiles, setSelectedFiles] = useState<MediaFile[]>([]);
  
  // Project image selector
  const projectSelector = useProjectImageSelector((file) => {
    console.log('Project image selected:', file);
    if (file) setSelectedFiles([file]);
  });

  // Blog image selector
  const blogSelector = useBlogImageSelector((files) => {
    console.log('Blog images selected:', files);
    setSelectedFiles(files);
  });

  // Product image selector
  const productSelector = useProductImageSelector((files) => {
    console.log('Product images selected:', files);
    setSelectedFiles(files);
  });

  // Document selector
  const documentSelector = useDocumentSelector((files) => {
    console.log('Documents selected:', files);
    setSelectedFiles(files);
  });

  return (
    <div className="p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
          Media Selector Examples
        </h1>
        <p className="text-muted-foreground">
          Showcase of the new enterprise-level media selector component
        </p>
      </div>

      {/* Selector Buttons */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={projectSelector.openSelector}>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <Image className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              Project Images
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">
              Select project images with cropping and optimization
            </p>
            <Button className="w-full" size="sm">
              <Upload className="h-4 w-4 mr-2" />
              Select Project Image
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={blogSelector.openSelector}>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <FileText className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              Blog Images
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">
              Multiple blog images with metadata editing
            </p>
            <Button className="w-full" size="sm" variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Select Blog Images
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={productSelector.openSelector}>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <Package className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              Product Images
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">
              Square product images with bulk operations
            </p>
            <Button className="w-full" size="sm" variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Select Product Images
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={documentSelector.openSelector}>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                <FileText className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              </div>
              Documents
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">
              PDF, Word, and text documents
            </p>
            <Button className="w-full" size="sm" variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Select Documents
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Selected Files Display */}
      {selectedFiles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Image className="h-5 w-5" />
              Selected Files ({selectedFiles.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {selectedFiles.map((file) => (
                <div
                  key={file.id}
                  className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-900 rounded-lg border"
                >
                  {/* Thumbnail */}
                  <div className="w-12 h-12 rounded-md overflow-hidden bg-gray-200 dark:bg-gray-700 flex-shrink-0">
                    {file.mimeType.startsWith('image/') ? (
                      <img
                        src={file.thumbnailSmall || file.url}
                        alt={file.alt || file.originalName}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-lg">
                        📄
                      </div>
                    )}
                  </div>

                  {/* File info */}
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm truncate">
                      {file.originalName}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {(file.size / 1024 / 1024).toFixed(2)} MB
                    </div>
                    {file.alt && (
                      <div className="text-xs text-muted-foreground truncate">
                        {file.alt}
                      </div>
                    )}
                  </div>

                  {/* Tags */}
                  {file.tags && file.tags.length > 0 && (
                    <div className="flex gap-1 flex-wrap">
                      {file.tags.slice(0, 2).map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {file.tags.length > 2 && (
                        <Badge variant="secondary" className="text-xs">
                          +{file.tags.length - 2}
                        </Badge>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>

            <div className="mt-4 flex gap-2">
              <Button
                variant="outline"
                onClick={() => setSelectedFiles([])}
                size="sm"
              >
                Clear Selection
              </Button>
              <Button
                onClick={() => {
                  console.log('Processing selected files:', selectedFiles);
                  // Here you would typically save or process the files
                }}
                size="sm"
              >
                Process Files
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Media Selectors */}
      <MediaSelector {...projectSelector} />
      <MediaSelector {...blogSelector} />
      <MediaSelector {...productSelector} />
      <MediaSelector {...documentSelector} />

      {/* Features List */}
      <Card>
        <CardHeader>
          <CardTitle>✨ Features</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <h4 className="font-medium">🎨 Modern Design</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Glass-morphism effects</li>
                <li>• Gradient backgrounds</li>
                <li>• Framer Motion animations</li>
                <li>• Responsive masonry grid</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">🚀 Advanced Features</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Real-time upload progress</li>
                <li>• Bulk operations</li>
                <li>• Advanced search & filtering</li>
                <li>• Metadata editing</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">⚡ Performance</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Context-based state management</li>
                <li>• Optimistic updates</li>
                <li>• Virtual scrolling ready</li>
                <li>• Lazy loading</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">🔧 Developer Experience</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• TypeScript-first</li>
                <li>• Highly configurable</li>
                <li>• Multiple layout modes</li>
                <li>• Enterprise-ready</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
