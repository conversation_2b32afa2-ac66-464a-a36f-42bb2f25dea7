"use client"

import React, { useState, useCallback } from 'react';
import { CropConfig } from '@/stores/media-store';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import Cropper, { Area } from 'react-easy-crop';
import { X, RotateCcw, ZoomIn, ZoomOut, Crop } from 'lucide-react';

interface AdvancedCropperProps {
  open: boolean;
  onClose: () => void;
  imageUrl: string | null;
  onCropComplete: (cropArea: Area, config: CropConfig) => void;
  config?: CropConfig;
}

export const AdvancedCropper: React.FC<AdvancedCropperProps> = ({
  open,
  onClose,
  imageUrl,
  onCropComplete,
  config
}) => {
  // Crop state
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);
  
  // Config state
  const [targetWidth, setTargetWidth] = useState(config?.targetWidth || 400);
  const [targetHeight, setTargetHeight] = useState(config?.targetHeight || 300);
  const [quality, setQuality] = useState(config?.quality || 90);
  const [maintainAspectRatio, setMaintainAspectRatio] = useState(!!config?.aspectRatio);
  
  // Calculate aspect ratio
  const aspectRatio = maintainAspectRatio ? targetWidth / targetHeight : undefined;

  const onCropCompleteCallback = useCallback(
    (croppedArea: Area, croppedAreaPixels: Area) => {
      console.log('✂️ CROPPER: Crop area updated', { croppedArea, croppedAreaPixels });
      setCroppedAreaPixels(croppedAreaPixels);
    },
    []
  );

  const handleCropConfirm = () => {
    if (!croppedAreaPixels) {
      console.error('❌ CROPPER: No crop area defined');
      return;
    }

    const cropConfig: CropConfig = {
      targetWidth,
      targetHeight,
      aspectRatio,
      quality
    };

    console.log('✅ CROPPER: Confirming crop', { croppedAreaPixels, cropConfig });
    onCropComplete(croppedAreaPixels, cropConfig);
  };

  const handleReset = () => {
    setCrop({ x: 0, y: 0 });
    setZoom(1);
    setRotation(0);
    setCroppedAreaPixels(null);
  };

  const handleWidthChange = (width: number) => {
    setTargetWidth(width);
    if (maintainAspectRatio && aspectRatio) {
      setTargetHeight(Math.round(width / aspectRatio));
    }
  };

  const handleHeightChange = (height: number) => {
    setTargetHeight(height);
    if (maintainAspectRatio && aspectRatio) {
      setTargetWidth(Math.round(height * aspectRatio));
    }
  };

  if (!imageUrl) return null;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[95vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Crop className="h-5 w-5" />
              <span>Advanced Image Cropper</span>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 flex gap-6 overflow-hidden">
          {/* Cropper area */}
          <div className="flex-1 relative bg-gray-100 rounded-lg overflow-hidden">
            <Cropper
              image={imageUrl}
              crop={crop}
              zoom={zoom}
              rotation={rotation}
              aspect={aspectRatio}
              onCropChange={setCrop}
              onZoomChange={setZoom}
              onRotationChange={setRotation}
              onCropComplete={onCropCompleteCallback}
              style={{
                containerStyle: {
                  width: '100%',
                  height: '100%',
                  backgroundColor: '#f3f4f6'
                }
              }}
            />
          </div>

          {/* Controls panel */}
          <div className="w-80 space-y-6 overflow-y-auto">
            {/* Dimension controls */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Output Dimensions</h3>
              
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label htmlFor="width">Width (px)</Label>
                  <Input
                    id="width"
                    type="number"
                    value={targetWidth}
                    onChange={(e) => handleWidthChange(parseInt(e.target.value) || 0)}
                    min="1"
                    max="4000"
                  />
                </div>
                <div>
                  <Label htmlFor="height">Height (px)</Label>
                  <Input
                    id="height"
                    type="number"
                    value={targetHeight}
                    onChange={(e) => handleHeightChange(parseInt(e.target.value) || 0)}
                    min="1"
                    max="4000"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="aspectRatio"
                  checked={maintainAspectRatio}
                  onChange={(e) => setMaintainAspectRatio(e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="aspectRatio">Maintain aspect ratio</Label>
              </div>

              {maintainAspectRatio && (
                <div className="text-sm text-gray-600">
                  Aspect ratio: {(targetWidth / targetHeight).toFixed(2)}:1
                </div>
              )}
            </div>

            {/* Zoom controls */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Zoom & Position</h3>
              
              <div>
                <div className="flex items-center justify-between mb-2">
                  <Label>Zoom</Label>
                  <span className="text-sm text-gray-600">{zoom.toFixed(1)}x</span>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setZoom(Math.max(1, zoom - 0.1))}
                  >
                    <ZoomOut className="h-3 w-3" />
                  </Button>
                  <Slider
                    value={[zoom]}
                    onValueChange={(value) => setZoom(value[0])}
                    min={1}
                    max={3}
                    step={0.1}
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setZoom(Math.min(3, zoom + 0.1))}
                  >
                    <ZoomIn className="h-3 w-3" />
                  </Button>
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <Label>Rotation</Label>
                  <span className="text-sm text-gray-600">{rotation}°</span>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setRotation(rotation - 90)}
                  >
                    <RotateCcw className="h-3 w-3" />
                  </Button>
                  <Slider
                    value={[rotation]}
                    onValueChange={(value) => setRotation(value[0])}
                    min={-180}
                    max={180}
                    step={1}
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setRotation(rotation + 90)}
                  >
                    <RotateCcw className="h-3 w-3 scale-x-[-1]" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Quality controls */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Output Quality</h3>
              
              <div>
                <div className="flex items-center justify-between mb-2">
                  <Label>JPEG Quality</Label>
                  <span className="text-sm text-gray-600">{quality}%</span>
                </div>
                <Slider
                  value={[quality]}
                  onValueChange={(value) => setQuality(value[0])}
                  min={10}
                  max={100}
                  step={5}
                />
              </div>
            </div>

            {/* Preview info */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Preview Info</h3>
              
              <div className="bg-gray-50 rounded p-3 space-y-2 text-sm">
                <div>Output size: {targetWidth} × {targetHeight}px</div>
                <div>Zoom level: {zoom.toFixed(1)}x</div>
                <div>Rotation: {rotation}°</div>
                <div>Quality: {quality}%</div>
                {croppedAreaPixels && (
                  <div className="text-xs text-gray-600 mt-2">
                    Crop area: {Math.round(croppedAreaPixels.width)} × {Math.round(croppedAreaPixels.height)}px
                  </div>
                )}
              </div>
            </div>

            {/* Action buttons */}
            <div className="space-y-3">
              <Button
                onClick={handleReset}
                variant="outline"
                className="w-full"
              >
                Reset
              </Button>
              
              <Button
                onClick={handleCropConfirm}
                className="w-full"
                disabled={!croppedAreaPixels}
              >
                Crop & Save
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
